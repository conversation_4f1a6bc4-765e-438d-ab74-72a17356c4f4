<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Chat Persistence Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 300px;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>Complete Chat Persistence Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <div class="info">
            <p><strong>This test verifies the complete chat persistence flow:</strong></p>
            <ol>
                <li>Load chats from backend API</li>
                <li>Store chats in localStorage</li>
                <li>Select a chat and load its messages</li>
                <li>Store current chat and messages in localStorage</li>
                <li>Simulate page refresh by clearing React state</li>
                <li>Verify that data is restored from localStorage</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>Step 1: Test Backend API</h2>
        <button onclick="testBackendAPI()">Test Backend Connection</button>
        <div id="backend-result"></div>
    </div>

    <div class="test-section">
        <h2>Step 2: Simulate Frontend Flow</h2>
        <button onclick="simulateCompleteFlow()">Run Complete Flow Test</button>
        <div id="flow-result"></div>
    </div>

    <div class="test-section">
        <h2>Step 3: Verify Persistence</h2>
        <button onclick="verifyPersistence()">Verify localStorage Persistence</button>
        <button onclick="clearAllData()">Clear All Data</button>
        <div id="persistence-result"></div>
    </div>

    <div class="test-section">
        <h2>Frontend Application Test</h2>
        <div class="info">
            <p><strong>Manual Test Steps:</strong></p>
            <ol>
                <li>Open the main frontend application: <a href="http://localhost:5174" target="_blank">http://localhost:5174</a></li>
                <li>Verify that chats appear in the left sidebar</li>
                <li>Click on a chat and verify messages load</li>
                <li>Send a new message</li>
                <li>Refresh the page (F5 or Ctrl+R)</li>
                <li>Verify that the same chat is selected and all messages are still there</li>
            </ol>
        </div>
    </div>

    <script>
        const STORAGE_KEYS = {
            CURRENT_CHAT: 'chat_current_chat',
            MESSAGES_CACHE: 'chat_messages_cache',
            CHATS_CACHE: 'chat_chats_cache'
        };

        async function testBackendAPI() {
            const resultDiv = document.getElementById('backend-result');
            try {
                // Test chats endpoint
                const chatsResponse = await fetch('http://localhost:8000/chats');
                if (!chatsResponse.ok) {
                    throw new Error(`Chats API error: ${chatsResponse.status}`);
                }
                const chats = await chatsResponse.json();

                // Test health endpoint
                const healthResponse = await fetch('http://localhost:8000/health');
                const health = await healthResponse.json();

                resultDiv.innerHTML = `
                    <div class="test-result success">✓ Backend API is working</div>
                    <div class="test-result info">
                        <strong>Available chats:</strong> ${chats.length}<br>
                        <strong>Backend status:</strong> ${health.status}
                    </div>
                    <pre>${JSON.stringify(chats, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">✗ Backend API error: ${error.message}</div>`;
            }
        }

        async function simulateCompleteFlow() {
            const resultDiv = document.getElementById('flow-result');
            const steps = [];

            try {
                // Step 1: Load chats from API
                steps.push('<div class="step">Step 1: Loading chats from API...</div>');
                const chatsResponse = await fetch('http://localhost:8000/chats');
                const chats = await chatsResponse.json();
                steps.push(`<div class="step">✓ Loaded ${chats.length} chats from API</div>`);

                // Step 2: Store chats in localStorage
                localStorage.setItem(STORAGE_KEYS.CHATS_CACHE, JSON.stringify(chats));
                steps.push('<div class="step">✓ Stored chats in localStorage</div>');

                // Step 3: Select first chat
                if (chats.length > 0) {
                    const firstChat = chats[0];
                    localStorage.setItem(STORAGE_KEYS.CURRENT_CHAT, JSON.stringify(firstChat));
                    steps.push(`<div class="step">✓ Selected chat: "${firstChat.title}"</div>`);

                    // Step 4: Load messages for the chat
                    const messagesResponse = await fetch(`http://localhost:8000/chats/${firstChat.id}/messages`);
                    const messages = await messagesResponse.json();
                    
                    // Step 5: Store messages in localStorage
                    const messagesCache = { [firstChat.id]: messages };
                    localStorage.setItem(STORAGE_KEYS.MESSAGES_CACHE, JSON.stringify(messagesCache));
                    steps.push(`<div class="step">✓ Loaded and stored ${messages.length} messages</div>`);

                    // Step 6: Verify persistence
                    const storedChats = JSON.parse(localStorage.getItem(STORAGE_KEYS.CHATS_CACHE));
                    const storedCurrentChat = JSON.parse(localStorage.getItem(STORAGE_KEYS.CURRENT_CHAT));
                    const storedMessages = JSON.parse(localStorage.getItem(STORAGE_KEYS.MESSAGES_CACHE));

                    if (storedChats && storedCurrentChat && storedMessages) {
                        steps.push('<div class="step">✓ All data successfully persisted to localStorage</div>');
                        
                        resultDiv.innerHTML = `
                            <div class="test-result success">✓ Complete flow test passed!</div>
                            ${steps.join('')}
                            <div class="test-result info">
                                <strong>Summary:</strong><br>
                                • Chats stored: ${storedChats.length}<br>
                                • Current chat: ${storedCurrentChat.title}<br>
                                • Messages for current chat: ${storedMessages[storedCurrentChat.id]?.length || 0}
                            </div>
                        `;
                    } else {
                        throw new Error('Data not properly stored in localStorage');
                    }
                } else {
                    steps.push('<div class="step">⚠ No chats available for testing</div>');
                    resultDiv.innerHTML = `
                        <div class="test-result info">No chats available for testing</div>
                        ${steps.join('')}
                    `;
                }
            } catch (error) {
                steps.push(`<div class="step">✗ Error: ${error.message}</div>`);
                resultDiv.innerHTML = `
                    <div class="test-result error">✗ Flow test failed</div>
                    ${steps.join('')}
                `;
            }
        }

        function verifyPersistence() {
            const resultDiv = document.getElementById('persistence-result');
            const data = {};
            
            Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
                const value = localStorage.getItem(key);
                data[name] = value ? JSON.parse(value) : null;
            });
            
            const hasChats = data.CHATS_CACHE && data.CHATS_CACHE.length > 0;
            const hasCurrentChat = data.CURRENT_CHAT !== null;
            const hasMessages = data.MESSAGES_CACHE && Object.keys(data.MESSAGES_CACHE).length > 0;
            
            const status = hasChats && hasCurrentChat && hasMessages ? 'success' : 'error';
            const statusText = status === 'success' ? '✓ Persistence verified' : '✗ Persistence incomplete';
            
            resultDiv.innerHTML = `
                <div class="test-result ${status}">${statusText}</div>
                <div class="test-result info">
                    <strong>Persistence Status:</strong><br>
                    • Chats cached: ${hasChats ? '✓' : '✗'} (${data.CHATS_CACHE?.length || 0} chats)<br>
                    • Current chat set: ${hasCurrentChat ? '✓' : '✗'}<br>
                    • Messages cached: ${hasMessages ? '✓' : '✗'} (${Object.keys(data.MESSAGES_CACHE || {}).length} chat histories)
                </div>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }

        function clearAllData() {
            Object.values(STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            document.getElementById('persistence-result').innerHTML = 
                '<div class="test-result success">✓ All chat data cleared from localStorage</div>';
        }

        // Auto-run backend test on page load
        window.onload = function() {
            testBackendAPI();
        };
    </script>
</body>
</html>
