import { useState, useEffect, useCallback, useRef } from 'react';
import './App.css';
import ChatSidebar from './components/ChatSidebar.jsx';
import DatabaseSidebar from './components/DatabaseSidebar.jsx';
import ChatWindow from './components/ChatWindow.jsx';
import { useChat, useDatabase } from './hooks/useApi.js';
import { aiClassifier } from './utils/aiMessageClassifier.js';
import { getUpdatedTitleIfNeeded, getBatchTitleUpdates } from './utils/chatTitleGenerator.js';

const App = () => {
  // Chat functionality
  const {
    loading: chatLoading,
    chats,
    currentChat,
    setCurrentChat,
    messages,
    loadChats,
    createChat,
    updateChatTitle,
    deleteChat,
    archiveChat,
    unarchiveChat,
    loadMessages,
    sendMessage,
    error: chatError
  } = useChat();

  // Database functionality
  const {
    loading: dbLoading,
    tables: dbTables,
    loadTables,
    error: dbError
  } = useDatabase();

  // Local state for selected table (not in hook)
  const [selectedTable, setSelectedTable] = useState(null);

  // Message input state
  const [prompt, setPrompt] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [queryResult, setQueryResult] = useState(null);
  const [showQueryResult, setShowQueryResult] = useState(false);
  const [appInitialized, setAppInitialized] = useState(false);

  // Ref to track if batch title update has run
  const batchTitleUpdateRan = useRef(false);

  // Load initial data
  useEffect(() => {
    // Load chats and database tables on app start
    const initializeApp = async () => {
      try {
        console.log('Starting app initialization...');
        await Promise.all([
          loadChats(false), // Load active chats
          loadTables()      // Load database tables
        ]);
        console.log('App initialization completed successfully');
        setAppInitialized(true);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        // Don't let initialization errors crash the app
        // The error will be shown in the error banner
        setAppInitialized(true); // Still show the app even if initialization fails
      }
    };

    initializeApp();
  }, [loadChats, loadTables]);

  // Restore current chat and its messages on app initialization (run only once)
  useEffect(() => {
    if (!appInitialized) return;

    const restoreCurrentChat = async () => {
      // If we have a current chat from localStorage but no messages loaded yet
      if (currentChat && currentChat.id && (!messages[currentChat.id] || messages[currentChat.id].length === 0)) {
        try {
          console.log('Restoring messages for current chat:', currentChat.id);
          await loadMessages(currentChat.id);
        } catch (error) {
          console.error('Failed to restore messages for current chat:', error);
        }
      }
    };

    restoreCurrentChat();
  }, [appInitialized, currentChat?.id]); // Only depend on appInitialized and currentChat.id

  // Auto-select first chat when chats are loaded (only if no current chat is set and app is initialized)
  useEffect(() => {
    if (appInitialized && chats.length > 0 && !currentChat && !chatLoading) {
      const firstChat = chats[0];
      setCurrentChat(firstChat);
      // Only load messages if they haven't been loaded yet
      const chatMessages = messages[firstChat.id];
      if (!chatMessages || chatMessages.length === 0) {
        loadMessages(firstChat.id);
      }
    }
  }, [appInitialized, chats.length, currentChat?.id, chatLoading]); // Optimized dependencies

  // Batch update generic chat titles when chats and messages are loaded (run only once after initialization)
  useEffect(() => {
    if (appInitialized && chats.length > 0 && Object.keys(messages).length > 0 && !batchTitleUpdateRan.current) {
      batchTitleUpdateRan.current = true;
      const titleUpdates = getBatchTitleUpdates(chats, messages);
      if (titleUpdates.length > 0) {
        console.log('Found', titleUpdates.length, 'chats with generic titles to update');
        // Update titles one by one to avoid overwhelming the API and prevent re-render issues
        const updateTitlesSequentially = async () => {
          for (const update of titleUpdates.slice(0, 2)) { // Limit to 2 at a time
            try {
              await updateChatTitle(update.chatId, update.newTitle);
              console.log(`Updated chat ${update.chatId} title from "${update.currentTitle}" to "${update.newTitle}"`);
              // Small delay to prevent rapid updates
              await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
              console.warn('Failed to update chat title:', error);
            }
          }
        };
        updateTitlesSequentially();
      }
    }
  }, [appInitialized, chats.length, Object.keys(messages).length, updateChatTitle]); // Minimal dependencies

  // Handle sending messages
  const handleSend = async () => {
    if (!prompt.trim() || isSubmitting) return;

    console.log('Starting handleSend with prompt:', prompt);
    setIsSubmitting(true);
    const messageToSend = prompt.trim();

    try {
      let chatToUse = currentChat;
      let isNewChat = false;

      console.log('Current chat:', currentChat);

      // If no chat is selected, create a new one first
      if (!chatToUse) {
        console.log('Creating new chat...');
        chatToUse = await createChat('New Chat', true);
        if (!chatToUse) {
          throw new Error('Failed to create new chat');
        }
        console.log('New chat created:', chatToUse);
        // Load messages for the new chat (will be empty initially)
        await loadMessages(chatToUse.id);
        isNewChat = true;
      }

      console.log('Sending message to chat:', chatToUse.id);
      const response = await sendMessage(chatToUse.id, messageToSend);
      console.log('Message response:', response);

      if (!response) {
        throw new Error('Failed to send message');
      }

      setPrompt('');

      // Update chat title if needed (after getting assistant response for better context)
      if (response && chatToUse) {
        // Get updated messages including the new ones
        const updatedMessages = messages[chatToUse.id] || [];
        const newTitle = getUpdatedTitleIfNeeded(chatToUse.title, updatedMessages);
        if (newTitle && newTitle !== chatToUse.title) {
          console.log('Updating chat title from:', chatToUse.title, 'to:', newTitle);
          await updateChatTitle(chatToUse.id, newTitle);
        }
      }

      // Use AI-powered classification instead of hardcoded patterns
      // This completely replaces all hardcoded conversational phrase detection

      try {
        // Use AI classifier to determine if query modal should be shown
        const shouldShowModal = await aiClassifier.shouldShowQueryModal(messageToSend, response);

        if (shouldShowModal) {
          if (response.query_result) {
            setQueryResult(response.query_result);
            setShowQueryResult(true);
          } else if (response.sql_query) {
            setQueryResult({ sql_query: response.sql_query });
            setShowQueryResult(true);
          }
        }
      } catch (error) {
        console.warn('AI classification failed, using fallback logic:', error);
        // Fallback: show modal if response has query data and message looks like a query
        const hasQueryData = response.query_result || response.sql_query;
        const looksLikeQuery = messageToSend.toLowerCase().includes('show') ||
                              messageToSend.toLowerCase().includes('list') ||
                              messageToSend.toLowerCase().includes('count');

        if (hasQueryData && looksLikeQuery) {
          if (response.query_result) {
            setQueryResult(response.query_result);
            setShowQueryResult(true);
          } else if (response.sql_query) {
            setQueryResult({ sql_query: response.sql_query });
            setShowQueryResult(true);
          }
        }
      }

      console.log('Message sent successfully');
    } catch (error) {
      console.error('Failed to send message:', error);
      // Show error to user
      alert(`Error: ${error.message || 'Failed to send message'}`);
      // Reset prompt on error so user can try again
      setPrompt(messageToSend);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if we can create a new chat (prevent if current chat is empty)
  const canCreateNewChat = useCallback(() => {
    if (!currentChat) return true; // No current chat, can create

    const currentMessages = messages[currentChat.id] || [];
    const hasUserMessages = currentMessages.some(msg => msg.role === 'user');

    return hasUserMessages; // Only allow if current chat has user messages
  }, [currentChat, messages]);

  // Handle creating new chat (memoized to prevent re-renders)
  const handleCreateNewChat = useCallback(async () => {
    if (!canCreateNewChat()) {
      alert('Please send a message in the current chat before creating a new one.');
      return;
    }

    try {
      console.log('Creating new chat and setting as current...');
      const newChat = await createChat('New Chat', true); // Set as current
      if (newChat) {
        console.log('New chat created and set as current:', newChat);
        // Load messages for the new chat (will be empty initially)
        await loadMessages(newChat.id);
      }
    } catch (error) {
      console.error('Failed to create new chat:', error);
    }
  }, [createChat, loadMessages, canCreateNewChat]);



  // Get current chat messages
  const displayMessages = currentChat ? (messages[currentChat.id] || []) : [];

  // Show loading screen during initialization
  if (!appInitialized) {
    return (
      <div className="app">
        <div className="loading-screen">
          <div className="loading-content">
            <div className="loading-spinner">⟳</div>
            <h2>Loading Chat Application...</h2>
            <p>Initializing your workspace</p>
          </div>
        </div>
      </div>
    );
  }

  // Fallback for critical errors
  if (!chats && !chatLoading && chatError) {
    return (
      <div className="app">
        <div className="error-screen">
          <div className="error-content">
            <h2>⚠️ Application Error</h2>
            <p>Unable to load the chat application.</p>
            <p><strong>Error:</strong> {chatError}</p>
            <button onClick={() => window.location.reload()}>Reload Application</button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="app">
      {(chatError || dbError) && (
        <div className="error-banner">
          {chatError && <div>Chat Error: {chatError}</div>}
          {dbError && <div>Database Error: {dbError}</div>}
        </div>
      )}


      <div className="app-content">
        <ChatSidebar
          chatLoading={chatLoading}
          chats={chats}
          currentChat={currentChat}
          setCurrentChat={setCurrentChat}
          loadMessages={loadMessages}
          createNewChat={handleCreateNewChat}
          canCreateNewChat={canCreateNewChat()}
          updateChatTitle={updateChatTitle}
          deleteChat={deleteChat}
          archiveChat={archiveChat}
          unarchiveChat={unarchiveChat}
          messages={messages}
        />

        <main className="main-content">
          <ChatWindow
            currentChat={currentChat}
            displayMessages={displayMessages}
            isSubmitting={isSubmitting}
            prompt={prompt}
            setPrompt={setPrompt}
            handleSend={handleSend}
            queryResult={queryResult}
            showQueryResult={showQueryResult}
            setShowQueryResult={setShowQueryResult}
          />
        </main>

        <DatabaseSidebar
          dbLoading={dbLoading}
          dbTables={dbTables}
          selectedTable={selectedTable}
          setSelectedTable={setSelectedTable}
        />
      </div>
    </div>
  );
};

export default App;