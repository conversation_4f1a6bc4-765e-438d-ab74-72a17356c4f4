/* Modern Lavender Theme - Complete UI Redesign */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Lavender Color Palette */
  --primary-50: #faf5ff;
  --primary-100: #f3e8ff;
  --primary-200: #e9d5ff;
  --primary-300: #d8b4fe;
  --primary-400: #c084fc;
  --primary-500: #a855f7;
  --primary-600: #9333ea;
  --primary-700: #7c3aed;
  --primary-800: #6b21a8;
  --primary-900: #581c87;
  --primary-950: #3b0764;

  /* Semantic Colors - Light Lavender Theme */
  --background-primary: linear-gradient(135deg, #fefcff 0%, #faf5ff 30%, #f3e8ff 100%);
  --background-secondary: linear-gradient(135deg, #ffffff 0%, #fefcff 50%, #faf5ff 100%);
  --background-card: rgba(255, 255, 255, 0.95);
  --background-hover: rgba(168, 85, 247, 0.06);
  --background-active: rgba(168, 85, 247, 0.1);
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-muted: #6b7280;
  --border-subtle: rgba(168, 85, 247, 0.12);
  --border-light: rgba(168, 85, 247, 0.2);
  --surface-secondary: #f8fafc;
  --shadow-soft: 0 4px 6px -1px rgba(168, 85, 247, 0.08), 0 2px 4px -1px rgba(168, 85, 247, 0.04);
  --shadow-medium: 0 10px 15px -3px rgba(168, 85, 247, 0.08), 0 4px 6px -2px rgba(168, 85, 247, 0.04);
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.2s ease-out;
  --transition-slow: 0.3s ease-out;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background: var(--background-primary);
  color: var(--text-primary);
  overflow: hidden;
}

#root {
  height: 100vh;
  width: 100vw;
}

/* APP STRUCTURE */
.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  position: relative;
}

.app-content {
  display: flex;
  flex: 1;
  height: 100vh;
  overflow: hidden;
}

/* ERROR BANNER */
.error-banner {
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
  color: #991b1b;
  padding: var(--space-3) var(--space-6);
  border-bottom: 1px solid #f87171;
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* LOADING SCREEN */
.loading-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  background: var(--background-primary);
}

.loading-content {
  text-align: center;
  color: var(--text-primary);
}

.loading-content .loading-spinner {
  font-size: 3rem;
  animation: spin 2s linear infinite;
  margin-bottom: var(--space-4);
  color: var(--primary-500);
}

.loading-content h2 {
  margin: var(--space-4) 0 var(--space-2) 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
}

.loading-content p {
  margin: 0;
  font-size: var(--font-size-base);
  color: var(--text-muted);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ERROR SCREEN */
.error-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  background: var(--background-primary);
}

.error-content {
  text-align: center;
  color: var(--text-primary);
  max-width: 500px;
  padding: var(--space-6);
}

.error-content h2 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--font-size-xl);
  color: #dc3545;
}

.error-content p {
  margin: var(--space-2) 0;
  font-size: var(--font-size-base);
  color: var(--text-muted);
}

.error-content button {
  margin-top: var(--space-4);
  padding: var(--space-3) var(--space-6);
  background-color: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--font-size-base);
  transition: background-color var(--transition-normal);
}

.error-content button:hover {
  background-color: var(--primary-600);
}

/* SIDEBAR COMPONENTS */
.sidebar {
  width: 250px;
  background: linear-gradient(135deg, #fefcff 0%, #f8f4ff 100%);
  border-right: 1px solid rgba(168, 85, 247, 0.08);
  padding: var(--space-4);
  overflow-y: auto;
  flex-shrink: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  position: relative;
  z-index: 100;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background-card);
  backdrop-filter: blur(20px);
  z-index: -1;
}

/* SIDEBAR HEADER */
.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  padding-bottom: 0;
}

.sidebar-header h2 {
  color: var(--primary-700);
  font-size: var(--font-size-2xl);
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.025em;
}

.loading-spinner {
  font-size: var(--font-size-lg);
  animation: spin 1s linear infinite;
  color: var(--primary-500);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* NEW CHAT BUTTON */
.new-chat-button-container {
  display: flex;
  justify-content: center;
}

.new-chat-button {
  width: auto;
  height: auto;
  padding: var(--space-3) var(--space-4);
  background: linear-gradient(135deg, #9333ea 0%, #a855f7 100%);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  position: relative;
  overflow: hidden;
  font-weight: 800;
  font-size: var(--font-size-base);
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
}

.new-chat-button:hover {
  background: linear-gradient(135deg, #8b2de2 0%, #9333ea 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(168, 85, 247, 0.4);
}

.new-chat-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.new-chat-button:hover {
  background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-600) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(168, 85, 247, 0.4);
}

.new-chat-button:hover::before {
  left: 100%;
}

.new-chat-button:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.25);
}

.new-chat-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--background-muted);
  color: var(--text-muted);
  transform: none;
  box-shadow: none;
}

.new-chat-button:disabled:hover {
  transform: none;
  box-shadow: none;
  background: var(--background-muted);
}

/* CHAT LIST STYLES */
.chat-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  flex: 1;
}

.loading {
  text-align: center;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  padding: var(--space-8);
}

.chat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
  margin-bottom: 1px;
  position: relative;
  overflow: visible;
}

.chat-item:hover {
  background: rgba(168, 85, 247, 0.08);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.15);
}

.chat-item:active {
  transform: translateX(1px);
  background: rgba(168, 85, 247, 0.12);
}

.chat-item.active {
  background: rgba(168, 85, 247, 0.12);
  color: var(--primary-700);
  font-weight: 600;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.15);
}

.chat-item:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 1px;
  border-radius: var(--radius-lg);
}

.chat-title {
  flex: 1;
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: var(--space-2);
}

.chat-actions {
  position: relative;
  display: flex;
  gap: var(--space-1);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 10;
  flex-shrink: 0;
}

.chat-item:hover .chat-actions,
.chat-actions:hover {
  opacity: 1;
}

/* 3-dot menu button */
.menu-btn {
  background: none;
  border: none;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.menu-btn:hover {
  background: rgba(168, 85, 247, 0.15);
  color: var(--primary-600);
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(168, 85, 247, 0.2);
}

.menu-btn:active {
  transform: scale(0.95);
  background: rgba(168, 85, 247, 0.2);
}

/* 3-dot menu dropdown */
.chat-menu {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(245, 212, 249, 0.25);
  min-width: 140px;
  padding: 4px;
  animation: menuSlideIn 0.15s ease-out;
  pointer-events: auto;
  user-select: none;
  z-index: 999999;
}

@keyframes menuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.chat-menu button {
  width: 100%;
  background: none;
  border: none;
  color: #333;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  text-align: left;
  margin-bottom: 1px;
  white-space: nowrap;
  pointer-events: auto;
  user-select: none;
  outline: none;
  position: relative;
  z-index: 1000000;
}

.chat-menu button:hover {
  background: #f5f5f5;
}

.chat-menu button.delete-option {
  color: #dc3545;
}

.chat-menu button.delete-option:hover {
  background: #ffeaea;
  color: #dc3545;
}

/* EDIT CHAT INPUT */
.edit-chat input {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-md);
  background: var(--background-card);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-family: inherit;
  outline: none;
  transition: all var(--transition-normal);
}

.edit-chat input:focus {
  border-color: var(--primary-500);
}

/* ARCHIVED SECTION */
.archived-section {
  margin-top: var(--space-6);
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-subtle);
}

.archived-section h3 {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin-bottom: var(--space-4);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.chat-item.archived {
  opacity: 0.7;
}

.chat-item.archived .chat-title {
  color: var(--text-muted);
}

/* DATABASE SIDEBAR */
.database-sidebar {
  width: 320px;
  background: linear-gradient(135deg, #fefcff 0%, #f8f4ff 100%);
  border-left: 1px solid rgba(168, 85, 247, 0.08);
  padding: var(--space-8);
  overflow-y: auto;
  flex-shrink: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-500);
  pointer-events: none;
  z-index: 1;
  transition: all var(--transition-fast);
}

.search-input {
  width: 100%;
  height: 44px;
  padding: var(--space-4) var(--space-4) var(--space-4) 48px;
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-xl);
  background: var(--background-card);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-family: inherit;
  outline: none;
  transition: all var(--transition-normal);
}

.search-input:focus {
  border-color: var(--primary-500);
  background: white;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
  transform: translateY(-1px);
}

.search-input:hover:not(:focus) {
  border-color: rgba(168, 85, 247, 0.3);
  background: white;
}

.search-input::placeholder {
  color: var(--text-muted);
  font-weight: 500;
}

.clear-search {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(168, 85, 247, 0.1);
  border: none;
  color: var(--primary-600);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.clear-search:hover {
  background: rgba(168, 85, 247, 0.2);
  color: var(--primary-700);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 2px 6px rgba(168, 85, 247, 0.25);
}

.clear-search:active {
  transform: translateY(-50%) scale(0.95);
  background: rgba(168, 85, 247, 0.25);
}

.tables-list {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.no-tables {
  text-align: center;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  font-style: italic;
}

/* .table-item {
  margin-bottom: 1px;
} */

.table-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
  margin-bottom: 1px;
}

.table-row:hover {
  background: rgba(168, 85, 247, 0.08);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.15);
}

.table-row:active {
  transform: translateX(1px);
  background: rgba(168, 85, 247, 0.12);
}

.table-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
  cursor: pointer;
}

.table-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-600);
  flex-shrink: 0;
  transition: all var(--transition-fast);
}

.table-info:hover .table-icon {
  transform: scale(1.1);
  color: var(--primary-700);
}

.table-name {
  flex: 1;
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.table-info:hover .table-name {
  color: var(--primary-600);
}

.expand-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.expand-btn:hover {
  background: rgba(168, 85, 247, 0.15);
  color: var(--primary-600);
  transform: scale(1.08);
  box-shadow: 0 2px 6px rgba(168, 85, 247, 0.2);
}

.expand-btn:active {
  transform: scale(0.95);
  background: rgba(168, 85, 247, 0.2);
}

.table-columns {
  padding: 0 var(--space-3) var(--space-2) var(--space-3);
  background: rgba(168, 85, 247, 0.02);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  animation: slideDown 0.3s ease-out;
  border-left: 2px solid rgba(168, 85, 247, 0.1);
  margin-left: var(--space-3);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 500px;
  }
}

.column-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-s);
  color: var(--text-muted);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.column-item:hover {
  background: rgba(168, 85, 247, 0.05);
  transform: translateX(2px);
}

.column-name {
  font-weight: 500;
  color: var(--text-secondary);
  flex: 1;
}

/* MAIN CONTENT AREA */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #fefcff 50%, #faf5ff 100%);
  position: relative;
}

/* CHAT WINDOW */
.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  max-width: 100%;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--text-muted);
  gap: var(--space-6);
  padding: var(--space-12);
  max-width: 768px;
  margin: 0 auto;
}

.empty-state h2 {
  color: var(--text-primary);
  font-size: var(--font-size-2xl);
  font-weight: 600;
  margin: 0;
}

.empty-state h3 {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.empty-state p {
  color: var(--text-muted);
  font-size: var(--font-size-lg);
  max-width: 500px;
  line-height: 1.6;
  margin: 0;
}

/* MESSAGE STYLES - Chat Bubble Style */
.message {
  width: 100%;
  display: flex;
  padding: var(--space-3) 0;
  animation: messageSlideIn 0.3s ease-out;
  margin-bottom: var(--space-2);
  justify-content: center;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.user {
  justify-content: center;
}

.message.user .message-wrapper {
  max-width: 900px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding: 0 var(--space-6);
}

.message.assistant {
  justify-content: center;
}

.message.assistant .message-wrapper {
  max-width: 900px;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  padding: 0 var(--space-6);
}

.message-content {
  max-width: 70%;
  padding: var(--space-4) var(--space-5);
  font-size: var(--font-size-base);
  line-height: 1.5;
  word-wrap: break-word;
  position: relative;
  color: var(--text-primary);
  border-radius: 18px;
}

.message.user .message-content {
  background: var(--primary-500);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
  background: var(--surface-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
  border-bottom-left-radius: 4px;
}

.message-timestamp {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  opacity: 0.6;
  margin-top: var(--space-2);
  text-align: left;
  max-width: 900px;
  width: 100%;
  padding: 0 var(--space-6);
  margin: 0 auto;
}

/* CODE BLOCKS */
.code-block {
  background: rgba(0, 0, 0, 0.08);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--font-size-sm);
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin: var(--space-3) 0;
  display: block;
  overflow-x: auto;
  color: var(--text-primary);
}

/* QUERY RESULT MODAL */
.query-result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.query-result-content {
  background: var(--background-card);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  box-shadow: var(--shadow-large);
  border: 1px solid var(--border-subtle);
}

.query-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.query-result-header h3 {
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.close-button:hover {
  background: var(--background-hover);
  color: var(--text-secondary);
}

/* INPUT CONTAINER - ChatGPT Style */
.input-container {
  padding: var(--space-6) var(--space-8);
  background: transparent;
  border-top: none;
  display: flex;
  justify-content: center;
}

.input-wrapper {
  display: flex;
  align-items: center;
  max-width: 900px;
  width: 100%;
  margin: 0 auto;
}

.message-input-container {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  background: white;
  border: 1px solid rgba(168, 85, 247, 0.2);
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal);
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.08);
  min-height: 100px;
}

.message-input-container:focus-within {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1), 0 4px 12px rgba(168, 85, 247, 0.15);
  transform: translateY(-1px);
}

.message-input-container:hover:not(:focus-within) {
  border-color: rgba(168, 85, 247, 0.3);
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.1);
}

.search-button {
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #8b5cf6 !important;
  border: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white !important;
  cursor: pointer;
  z-index: 1;
  transition: all var(--transition-fast);
  padding: 0 !important;
  font-size: 1em !important;
  font-weight: normal !important;
  font-family: inherit !important;
}

.search-button:hover:not(:disabled) {
  background: #7c3aed !important;
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #9ca3af !important;
}

.search-button svg {
  display: block !important;
  width: 16px !important;
  height: 16px !important;
  stroke: white !important;
  stroke-width: 2 !important;
  fill: none !important;
}

.search-button .loading-dots {
  display: flex;
  gap: 2px;
}

.search-button .loading-dots span {
  width: 3px;
  height: 3px;
  background: white;
  border-radius: 50%;
}

.message-input {
  flex: 1;
  min-height: 24px;
  max-height: 200px;
  padding: var(--space-4) 56px var(--space-4) var(--space-5);
  border: none;
  border-radius: var(--radius-xl);
  background: transparent;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-family: inherit;
  line-height: 1.5;
  resize: none;
  outline: none;
  transition: all var(--transition-normal);
  caret-color: var(--primary-500);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.message-input::placeholder {
  color: var(--text-muted);
  font-weight: 500;
}

.send-button {
  position: absolute;
  right: var(--space-2);
  top: 50%;
  transform: translateY(-50%);
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #9333ea 0%, #a855f7 100%);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.3);
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-600) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.send-button:active {
  transform: translateY(0);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  transform: none;
}

.send-button:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-soft);
}

/* RESPONSIVE DESIGN */
@media (max-width: 1024px) {
  .sidebar {
    width: 220px;
  }

  .database-sidebar {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .sidebar,
  .database-sidebar {
    display: none;
  }

  .main-content {
    width: 100vw;
  }

  .chat-messages {
    padding: 0;
  }

  .input-container {
    padding: var(--space-4) var(--space-4);
  }

  .input-wrapper {
    max-width: 100%;
  }

  .message-content {
    padding: var(--space-4) var(--space-4);
  }

  .message-timestamp {
    padding: 0 var(--space-4);
  }

  .message.user .message-wrapper,
  .message.assistant .message-wrapper {
    padding: 0 var(--space-4);
  }
}

/* SCROLLBAR STYLING */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border-subtle);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-300);
}

/* FOCUS STYLES FOR ACCESSIBILITY */
*:focus-visible:not(input):not(textarea) {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

input:focus-visible,
textarea:focus-visible {
  outline: none;
}

button:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-radius: inherit;
}

.chat-item:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 1px;
  border-radius: var(--radius-lg);
}

.table-row:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 1px;
  border-radius: var(--radius-lg);
}

/* LOADING STATES */
.loading-dots {
  display: inline-flex;
  gap: 3px;
  align-items: center;
  justify-content: center;
}

.loading-dots span {
  width: 4px;
  height: 4px;
  background: currentColor;
  border-radius: 50%;
  animation: loadingDots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.4;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* RIPPLE EFFECT */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}
