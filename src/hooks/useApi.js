import { useState, useCallback, useEffect } from 'react';
import { chatAPI, databaseAPI } from '../services/api.js';
import { saveToStorage, loadFromStorage, removeFromStorage } from '../utils/storage';
import { getUserFriendlyErrorMessage } from '../utils/networkUtils';

// Storage keys for persistence
const STORAGE_KEYS = {
  CURRENT_CHAT: 'chat_current_chat',
  MESSAGES_CACHE: 'chat_messages_cache',
  CHATS_CACHE: 'chat_chats_cache'
};



// Custom hook for chat functionality
export const useChat = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [chats, setChats] = useState(() => loadFromStorage(STORAGE_KEYS.CHATS_CACHE, []));
  const [currentChat, setCurrentChat] = useState(() => loadFromStorage(STORAGE_KEYS.CURRENT_CHAT, null));
  const [messages, setMessages] = useState(() => loadFromStorage(STORAGE_KEYS.MESSAGES_CACHE, {}));
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Enhanced state setters with persistence
  const setChatsWithPersistence = useCallback((newChats) => {
    if (typeof newChats === 'function') {
      setChats(prev => {
        const updated = newChats(prev);
        saveToStorage(STORAGE_KEYS.CHATS_CACHE, updated);
        return updated;
      });
    } else {
      setChats(newChats);
      saveToStorage(STORAGE_KEYS.CHATS_CACHE, newChats);
    }
  }, []);

  const setCurrentChatWithPersistence = useCallback((newChat) => {
    setCurrentChat(newChat);
    saveToStorage(STORAGE_KEYS.CURRENT_CHAT, newChat);
  }, []);

  const setMessagesWithPersistence = useCallback((newMessages) => {
    if (typeof newMessages === 'function') {
      setMessages(prev => {
        const updated = newMessages(prev);
        saveToStorage(STORAGE_KEYS.MESSAGES_CACHE, updated);
        return updated;
      });
    } else {
      setMessages(newMessages);
      saveToStorage(STORAGE_KEYS.MESSAGES_CACHE, newMessages);
    }
  }, []);

  // Network status monitoring
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Reset error
  const reset = useCallback(() => {
    setError(null);
  }, []);

  // Clear all stored data (useful for logout or reset)
  const clearStoredData = useCallback(() => {
    removeFromStorage(STORAGE_KEYS.CURRENT_CHAT);
    removeFromStorage(STORAGE_KEYS.MESSAGES_CACHE);
    removeFromStorage(STORAGE_KEYS.CHATS_CACHE);
    setCurrentChat(null);
    setMessages({});
    setChats([]);
  }, []);

  // Load all chats
  const loadChats = useCallback(async (includeArchived = false) => {
    try {
      setLoading(true);
      setError(null);
      const chatData = await chatAPI.getChats(includeArchived);
      setChatsWithPersistence(chatData);
    } catch (err) {
      setError(getUserFriendlyErrorMessage(err));
      console.error('Error loading chats:', err);
    } finally {
      setLoading(false);
    }
  }, [setChatsWithPersistence]);

  // Create new chat
  const createChat = useCallback(async (title = 'New Chat', setAsCurrent = false) => {
    try {
      setLoading(true);
      setError(null);
      const newChat = await chatAPI.createChat(title);
      setChatsWithPersistence(prev => [newChat, ...prev]);

      // Optionally set as current chat
      if (setAsCurrent) {
        setCurrentChatWithPersistence(newChat);
      }

      return newChat;
    } catch (err) {
      setError(err.message || 'Failed to create chat');
      console.error('Error creating chat:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, [setChatsWithPersistence, setCurrentChatWithPersistence]);

  // Load messages for a specific chat
  const loadMessages = useCallback(async (chatId) => {
    try {
      setLoading(true);
      setError(null);
      const messageData = await chatAPI.getMessages(chatId);
      setMessagesWithPersistence(prev => ({
        ...prev,
        [chatId]: messageData
      }));
    } catch (err) {
      setError(getUserFriendlyErrorMessage(err));
      console.error('Error loading messages:', err);
    } finally {
      setLoading(false);
    }
  }, [setMessagesWithPersistence]);

  // Send message
  const sendMessage = useCallback(async (chatId, message) => {
    try {
      setLoading(true);
      setError(null);
      const response = await chatAPI.sendMessage(chatId, message);

      // Handle backend response structure - backend returns messages array
      if (response.messages && Array.isArray(response.messages)) {
        // Backend returns both user and assistant messages
        setMessagesWithPersistence(prev => ({
          ...prev,
          [chatId]: [
            ...(prev[chatId] || []),
            ...response.messages.map(msg => ({
              role: msg.role,
              content: msg.content,
              timestamp: msg.created_at || new Date().toISOString()
            }))
          ]
        }));
      } else if (response.response) {
        // Fallback for old response format
        setMessagesWithPersistence(prev => ({
          ...prev,
          [chatId]: [
            ...(prev[chatId] || []),
            { role: 'user', content: message, timestamp: new Date().toISOString() },
            { role: 'assistant', content: response.response, timestamp: new Date().toISOString() }
          ]
        }));
      }

      return response;
    } catch (err) {
      setError(getUserFriendlyErrorMessage(err));
      console.error('Error sending message:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, [setMessagesWithPersistence]);

  // Update chat title
  const updateChatTitle = useCallback(async (chatId, title) => {
    try {
      setLoading(true);
      setError(null);
      const updatedChat = await chatAPI.updateChat(chatId, { title });
      setChatsWithPersistence(prev => prev.map(chat =>
        chat.id === chatId ? updatedChat : chat
      ));

      // Update current chat if it's the one being updated
      if (currentChat && currentChat.id === chatId) {
        setCurrentChatWithPersistence(updatedChat);
      }
    } catch (err) {
      setError(err.message || 'Failed to update chat title');
      console.error('Error updating chat title:', err);
    } finally {
      setLoading(false);
    }
  }, [setChatsWithPersistence, setCurrentChatWithPersistence, currentChat]);

  // Delete chat
  const deleteChat = useCallback(async (chatId) => {
    try {
      setLoading(true);
      setError(null);
      await chatAPI.deleteChat(chatId);
      setChatsWithPersistence(prev => prev.filter(chat => chat.id !== chatId));

      // Clear messages for deleted chat
      setMessagesWithPersistence(prev => {
        const newMessages = { ...prev };
        delete newMessages[chatId];
        return newMessages;
      });

      // Clear current chat if it was deleted
      if (currentChat && currentChat.id === chatId) {
        setCurrentChatWithPersistence(null);
      }
    } catch (err) {
      setError(err.message || 'Failed to delete chat');
      console.error('Error deleting chat:', err);
    } finally {
      setLoading(false);
    }
  }, [currentChat, setChatsWithPersistence, setMessagesWithPersistence, setCurrentChatWithPersistence]);

  // Archive chat
  const archiveChat = useCallback(async (chatId) => {
    try {
      setLoading(true);
      setError(null);
      const result = await chatAPI.archiveChat(chatId);
      setChatsWithPersistence(prev => prev.map(chat =>
        chat.id === chatId ? result.chat : chat
      ));
    } catch (err) {
      setError(err.message || 'Failed to archive chat');
      console.error('Error archiving chat:', err);
    } finally {
      setLoading(false);
    }
  }, [setChatsWithPersistence]);

  // Unarchive chat
  const unarchiveChat = useCallback(async (chatId) => {
    try {
      setLoading(true);
      setError(null);
      const result = await chatAPI.unarchiveChat(chatId);
      setChatsWithPersistence(prev => prev.map(chat =>
        chat.id === chatId ? result.chat : chat
      ));
    } catch (err) {
      setError(err.message || 'Failed to unarchive chat');
      console.error('Error unarchiving chat:', err);
    } finally {
      setLoading(false);
    }
  }, [setChatsWithPersistence]);

  return {
    loading,
    error,
    reset,
    clearStoredData,
    chats,
    currentChat,
    messages,
    setCurrentChat: setCurrentChatWithPersistence,
    loadChats,
    createChat,
    loadMessages,
    sendMessage,
    updateChatTitle,
    deleteChat,
    archiveChat,
    unarchiveChat,
    isOnline
  };
};

// Custom hook for database functionality
export const useDatabase = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [tables, setTables] = useState([]);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Network status monitoring
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Reset error
  const reset = useCallback(() => {
    setError(null);
  }, []);

  // Load database tables
  const loadTables = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const tableData = await databaseAPI.getTables();
      setTables(tableData);
    } catch (err) {
      setError(err.message || 'Failed to load database tables');
      console.error('Error loading tables:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Execute query
  const executeQuery = useCallback(async (query, selectedTables = []) => {
    try {
      setLoading(true);
      setError(null);
      const result = await databaseAPI.executeQuery(query, selectedTables);
      return result;
    } catch (err) {
      setError(err.message || 'Failed to execute query');
      console.error('Error executing query:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    reset,
    tables,
    loadTables,
    executeQuery,
    isOnline
  };
};
