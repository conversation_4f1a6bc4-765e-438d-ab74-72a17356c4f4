/**
 * Utility functions for localStorage operations
 * Provides safe storage operations with error handling
 */

/**
 * Save data to localStorage with error handling
 * @param {string} key - The storage key
 * @param {any} data - The data to store (will be JSON stringified)
 */
export const saveToStorage = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.warn('Failed to save to localStorage:', error);
    // Handle quota exceeded or other storage errors gracefully
    if (error.name === 'QuotaExceededError') {
      console.warn('localStorage quota exceeded. Attempting to clear old data...');
      clearOldStorageData();
      // Try again after clearing
      try {
        localStorage.setItem(key, JSON.stringify(data));
      } catch (retryError) {
        console.error('Failed to save to localStorage even after clearing:', retryError);
      }
    }
  }
};

/**
 * Load data from localStorage with error handling
 * @param {string} key - The storage key
 * @param {any} defaultValue - Default value to return if key doesn't exist or parsing fails
 * @returns {any} The parsed data or default value
 */
export const loadFromStorage = (key, defaultValue = null) => {
  try {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : defaultValue;
  } catch (error) {
    console.warn('Failed to load from localStorage:', error);
    return defaultValue;
  }
};

/**
 * Remove data from localStorage with error handling
 * @param {string} key - The storage key to remove
 */
export const removeFromStorage = (key) => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.warn('Failed to remove from localStorage:', error);
  }
};

/**
 * Clear all data from localStorage
 */
export const clearAllStorage = () => {
  try {
    localStorage.clear();
  } catch (error) {
    console.warn('Failed to clear localStorage:', error);
  }
};

/**
 * Get the size of localStorage in bytes (approximate)
 * @returns {number} Approximate size in bytes
 */
export const getStorageSize = () => {
  try {
    let total = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += localStorage[key].length + key.length;
      }
    }
    return total;
  } catch (error) {
    console.warn('Failed to calculate storage size:', error);
    return 0;
  }
};

/**
 * Clear old storage data to free up space
 * This removes non-essential cached data
 */
const clearOldStorageData = () => {
  try {
    // List of keys that are safe to remove when storage is full
    const clearableKeys = [
      'chat_messages_cache', // Can be reloaded from server
      'temp_', // Any temporary data
      'cache_', // Any cached data
    ];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && clearableKeys.some(prefix => key.startsWith(prefix))) {
        localStorage.removeItem(key);
        i--; // Adjust index since we removed an item
      }
    }
  } catch (error) {
    console.warn('Failed to clear old storage data:', error);
  }
};

/**
 * Check if localStorage is available
 * @returns {boolean} True if localStorage is available
 */
export const isStorageAvailable = () => {
  try {
    const test = '__storage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Get all keys in localStorage that match a prefix
 * @param {string} prefix - The prefix to match
 * @returns {string[]} Array of matching keys
 */
export const getKeysWithPrefix = (prefix) => {
  try {
    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        keys.push(key);
      }
    }
    return keys;
  } catch (error) {
    console.warn('Failed to get keys with prefix:', error);
    return [];
  }
};

/**
 * Safely update storage with a function
 * @param {string} key - The storage key
 * @param {function} updateFn - Function that receives current value and returns new value
 * @param {any} defaultValue - Default value if key doesn't exist
 */
export const updateStorage = (key, updateFn, defaultValue = null) => {
  try {
    const currentValue = loadFromStorage(key, defaultValue);
    const newValue = updateFn(currentValue);
    saveToStorage(key, newValue);
    return newValue;
  } catch (error) {
    console.warn('Failed to update storage:', error);
    return defaultValue;
  }
};
