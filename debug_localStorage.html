<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug localStorage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #ddd;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Debug localStorage for Chat App</h1>
    
    <div class="section">
        <h2>Current localStorage Data</h2>
        <button onclick="showLocalStorage()">Refresh localStorage Data</button>
        <button onclick="clearChatData()">Clear Chat Data</button>
        <pre id="localStorage-data">Click "Refresh localStorage Data" to see current data</pre>
    </div>

    <div class="section">
        <h2>Test API Connection</h2>
        <button onclick="testAPI()">Test Chat API</button>
        <pre id="api-data">Click "Test Chat API" to test backend connection</pre>
    </div>

    <script>
        const STORAGE_KEYS = {
            CURRENT_CHAT: 'chat_current_chat',
            MESSAGES_CACHE: 'chat_messages_cache',
            CHATS_CACHE: 'chat_chats_cache'
        };

        function showLocalStorage() {
            const data = {};
            
            // Get all localStorage items
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.startsWith('chat_')) {
                    try {
                        data[key] = JSON.parse(localStorage.getItem(key));
                    } catch (e) {
                        data[key] = localStorage.getItem(key);
                    }
                }
            }
            
            document.getElementById('localStorage-data').textContent = JSON.stringify(data, null, 2);
        }

        function clearChatData() {
            Object.values(STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            showLocalStorage();
            alert('Chat data cleared from localStorage');
        }

        async function testAPI() {
            try {
                const response = await fetch('http://localhost:8000/chats');
                if (response.ok) {
                    const chats = await response.json();
                    document.getElementById('api-data').textContent = 
                        `API Status: OK\nChats from backend:\n${JSON.stringify(chats, null, 2)}`;
                } else {
                    document.getElementById('api-data').textContent = 
                        `API Error: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                document.getElementById('api-data').textContent = 
                    `API Connection Error: ${error.message}`;
            }
        }

        // Auto-load on page load
        window.onload = function() {
            showLocalStorage();
        };
    </script>
</body>
</html>
