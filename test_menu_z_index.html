<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu Z-Index Test - Above Main Content</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
        }
        
        /* Simulate the app layout */
        .app-layout {
            display: flex;
            width: 100%;
            height: 100vh;
        }
        
        .sidebar-demo {
            width: 240px;
            background: linear-gradient(135deg, #fefcff 0%, #f8f4ff 100%);
            border-right: 1px solid rgba(168, 85, 247, 0.08);
            padding: 16px;
            overflow-y: auto;
            position: relative;
            z-index: 100;
        }
        
        .main-content-demo {
            flex: 1;
            background: linear-gradient(135deg, #ffffff 0%, #fefcff 50%, #faf5ff 100%);
            position: relative;
            padding: 20px;
            overflow: hidden;
        }
        
        /* High z-index content to test against */
        .high-z-content {
            position: absolute;
            top: 50px;
            left: 50px;
            right: 50px;
            bottom: 50px;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        /* Chat item styling */
        .demo-chat-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 4px 8px;
            border-radius: 12px;
            transition: all 0.15s;
            cursor: pointer;
            margin-bottom: 8px;
            position: relative;
            overflow: visible;
        }
        
        .demo-chat-item:hover {
            background: rgba(168, 85, 247, 0.08);
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(168, 85, 247, 0.15);
        }
        
        .demo-chat-actions {
            position: relative;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s;
            z-index: 10;
            flex-shrink: 0;
        }
        
        .demo-chat-item:hover .demo-chat-actions,
        .demo-chat-actions:hover {
            opacity: 1;
        }
        
        .demo-menu-btn {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.15s;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
        }
        
        .demo-menu-btn:hover {
            background: rgba(168, 85, 247, 0.15);
            color: #a855f7;
            transform: scale(1.1);
            box-shadow: 0 2px 4px rgba(168, 85, 247, 0.2);
        }
        
        /* Fixed positioned menu with very high z-index */
        .demo-chat-menu {
            position: fixed;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
            z-index: 99999;
            min-width: 140px;
            padding: 4px;
            animation: menuSlideIn 0.15s ease-out;
            pointer-events: auto;
            user-select: none;
        }
        
        @keyframes menuSlideIn {
            from {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .demo-chat-menu button {
            width: 100%;
            background: none;
            border: none;
            color: #333;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 4px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            text-align: left;
            margin-bottom: 1px;
            white-space: nowrap;
            pointer-events: auto;
            user-select: none;
            outline: none;
        }
        
        .demo-chat-menu button:hover {
            background: #f5f5f5;
        }
        
        .demo-chat-menu button.delete-option {
            color: #dc3545;
        }
        
        .demo-chat-menu button.delete-option:hover {
            background: #ffeaea;
            color: #dc3545;
        }
        
        .status {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            padding: 10px;
            background: rgba(212, 237, 218, 0.95);
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            z-index: 50;
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <div class="sidebar-demo">
            <h3 style="margin-top: 0; font-size: 16px;">Chat Sidebar</h3>
            
            <div class="demo-chat-item">
                <span>SQL Query Analysis</span>
                <div class="demo-chat-actions">
                    <button class="demo-menu-btn" onclick="toggleDemoMenu(event)">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="1"/>
                            <circle cx="12" cy="5" r="1"/>
                            <circle cx="12" cy="19" r="1"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="demo-chat-item">
                <span>Performance Optimization</span>
                <div class="demo-chat-actions">
                    <button class="demo-menu-btn" onclick="toggleDemoMenu(event)">⋮</button>
                </div>
            </div>
            
            <div class="demo-chat-item">
                <span>Data Analysis</span>
                <div class="demo-chat-actions">
                    <button class="demo-menu-btn" onclick="toggleDemoMenu(event)">⋮</button>
                </div>
            </div>
        </div>
        
        <div class="main-content-demo">
            <div class="status">
                <strong>✅ Z-Index Test:</strong> The menu should appear ABOVE this main content area, even though this has z-index: 1000
            </div>
            
            <div class="high-z-content">
                <div>
                    <h2>High Z-Index Content (z-index: 1000)</h2>
                    <p>This content has z-index: 1000 to simulate the main chat area.</p>
                    <p><strong>The menu should appear ABOVE this content when you click the ⋮ button.</strong></p>
                    <p>If the menu appears behind this, there's a z-index issue.</p>
                    <br>
                    <p><strong>Menu z-index: 99999</strong> (should be higher than this content)</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Fixed positioned menu -->
    <div class="demo-chat-menu" id="test-menu" style="display: none;">
        <button onclick="testAction('Rename')">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
            </svg>
            Rename
        </button>
        <button onclick="testAction('Share')">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                <polyline points="16,6 12,2 8,6"/>
                <line x1="12" y1="2" x2="12" y2="15"/>
            </svg>
            Share
        </button>
        <button onclick="testAction('Archive')">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <path d="M9 9h6v6H9z"/>
            </svg>
            Archive
        </button>
        <button onclick="testAction('Delete')" class="delete-option">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z"/>
                <path d="M10 11v6M14 11v6"/>
            </svg>
            Delete
        </button>
    </div>

    <script>
        let menuOpen = false;
        
        function toggleDemoMenu(event) {
            const menu = document.getElementById('test-menu');
            
            if (menuOpen) {
                menu.style.display = 'none';
                menuOpen = false;
                return;
            }
            
            // Get button position
            const buttonRect = event.currentTarget.getBoundingClientRect();
            
            // Position to the right of the button
            let left = buttonRect.right + 8;
            let top = buttonRect.top;
            
            // Check if menu would go off-screen horizontally
            const menuWidth = 140;
            if (left + menuWidth > window.innerWidth) {
                left = buttonRect.left - menuWidth - 8;
            }
            
            // Check if menu would go off-screen vertically
            const menuHeight = 160;
            if (top + menuHeight > window.innerHeight) {
                top = window.innerHeight - menuHeight - 8;
            }
            
            menu.style.left = `${left}px`;
            menu.style.top = `${top}px`;
            menu.style.display = 'block';
            menuOpen = true;
        }
        
        function testAction(action) {
            alert(`${action} clicked! Menu appeared ABOVE the main content area! ✅`);
            document.getElementById('test-menu').style.display = 'none';
            menuOpen = false;
        }
        
        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.demo-chat-actions') && !event.target.closest('.demo-chat-menu')) {
                document.getElementById('test-menu').style.display = 'none';
                menuOpen = false;
            }
        });
    </script>
</body>
</html>
