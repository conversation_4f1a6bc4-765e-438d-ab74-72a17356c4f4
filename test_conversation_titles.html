<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Conversation-Based Title Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .conversation {
            background-color: #e3f2fd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .user-msg {
            color: #1976d2;
            font-weight: bold;
        }
        .assistant-msg {
            color: #388e3c;
            font-style: italic;
        }
        .title-result {
            background-color: #fff3e0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-weight: bold;
            color: #f57c00;
        }
        button {
            margin: 10px 5px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Conversation-Based Title Generation Test</h1>
    
    <div>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="testResults"></div>

    <script>
        // Copy the improved title generation functions
        const GENERIC_TITLES = [
            'New Chat',
            'hello',
            'hi',
            'hey',
            'test',
            'testing',
            'chat',
            'conversation'
        ];

        const isGenericTitle = (title) => {
            if (!title || typeof title !== 'string') return true;
            const normalizedTitle = title.toLowerCase().trim();
            if (GENERIC_TITLES.includes(normalizedTitle)) return true;
            const greetingPattern = /^(hi|hello|hey|hiya|howdy)[\s!.]*$/i;
            if (greetingPattern.test(normalizedTitle)) return true;
            if (normalizedTitle.length <= 2) return true;
            return false;
        };

        const analyzeConversationContext = (userMessage, assistantMessage = '') => {
            const userLower = userMessage.toLowerCase();
            const assistantLower = assistantMessage.toLowerCase();
            
            if (userLower.includes('select') || userLower.includes('sql') || userLower.includes('query')) {
                if (assistantLower.includes('result') || assistantLower.includes('data')) {
                    return { title: 'SQL Query Analysis' };
                }
                return { title: 'Database Query' };
            }
            
            if (userLower.includes('table') || userLower.includes('schema') || userLower.includes('column')) {
                if (assistantLower.includes('relationship') || assistantLower.includes('foreign key')) {
                    return { title: 'Database Schema Analysis' };
                }
                return { title: 'Table Structure' };
            }
            
            if (userLower.includes('report') || userLower.includes('analysis') || userLower.includes('trend')) {
                return { title: 'Data Analysis' };
            }
            
            if (userLower.includes('performance') || userLower.includes('optimize') || userLower.includes('slow')) {
                return { title: 'Performance Optimization' };
            }
            
            if (userLower.includes('user') || userLower.includes('permission') || userLower.includes('access')) {
                return { title: 'User Management' };
            }
            
            if (userLower.includes('error') || userLower.includes('problem') || userLower.includes('issue')) {
                return { title: 'Troubleshooting' };
            }
            
            return { title: null };
        };

        const extractKeyWords = (message) => {
            const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'];
            
            const words = message.toLowerCase()
                .replace(/[^\w\s]/g, '')
                .split(/\s+/)
                .filter(word => word.length > 2 && !commonWords.includes(word))
                .slice(0, 3);
            
            return words;
        };

        const generateContextualTitle = (userMessage, assistantMessage = '') => {
            const context = analyzeConversationContext(userMessage, assistantMessage);
            
            if (context.title) {
                return context.title;
            }
            
            let cleanMessage = userMessage.trim();
            
            const prefixesToRemove = [
                /^(can you|could you|please|would you|will you)\s+/i,
                /^(i want to|i need to|i would like to|i'd like to)\s+/i,
                /^(help me|show me|tell me|explain)\s+/i,
                /^(what is|what are|how do|how can|where is)\s+/i
            ];
            
            prefixesToRemove.forEach(pattern => {
                cleanMessage = cleanMessage.replace(pattern, '');
            });
            
            cleanMessage = cleanMessage.replace(/[?!]+$/, '');
            cleanMessage = cleanMessage.charAt(0).toUpperCase() + cleanMessage.slice(1);
            
            if (cleanMessage.length > 50) {
                const truncated = cleanMessage.substring(0, 47);
                const lastSpace = truncated.lastIndexOf(' ');
                if (lastSpace > 20) {
                    cleanMessage = truncated.substring(0, lastSpace) + '...';
                } else {
                    cleanMessage = truncated + '...';
                }
            }
            
            if (cleanMessage.length < 3 || isGenericTitle(cleanMessage)) {
                const keyWords = extractKeyWords(userMessage);
                if (keyWords.length > 0) {
                    return keyWords.slice(0, 2).map(word => 
                        word.charAt(0).toUpperCase() + word.slice(1)
                    ).join(' ') + ' Discussion';
                }
                return 'Chat Discussion';
            }
            
            return cleanMessage;
        };

        const generateTitleFromConversation = (messages) => {
            if (!messages || !Array.isArray(messages) || messages.length === 0) {
                return 'New Chat';
            }

            const userMessage = messages.find(msg => msg.role === 'user');
            const assistantMessage = messages.find(msg => msg.role === 'assistant');
            
            if (!userMessage) {
                return 'New Chat';
            }

            const userContent = userMessage.content.trim();
            const assistantContent = assistantMessage ? assistantMessage.content.trim() : '';
            
            return generateContextualTitle(userContent, assistantContent);
        };

        // Test cases with conversation context
        const testConversations = [
            {
                name: "Database Query Conversation",
                messages: [
                    { role: 'user', content: 'Can you show me all users from the database?' },
                    { role: 'assistant', content: 'Here\'s a SQL query to get all users: SELECT * FROM users. The result shows 150 users in the database.' }
                ],
                expected: 'SQL Query Analysis'
            },
            {
                name: "Table Structure Inquiry",
                messages: [
                    { role: 'user', content: 'What columns are in the customers table?' },
                    { role: 'assistant', content: 'The customers table has the following columns: id, name, email, phone, created_at. There are foreign key relationships to the orders table.' }
                ],
                expected: 'Database Schema Analysis'
            },
            {
                name: "Performance Issue",
                messages: [
                    { role: 'user', content: 'My queries are running very slow, can you help optimize them?' },
                    { role: 'assistant', content: 'I can help you optimize your database performance. Let\'s start by analyzing your query patterns.' }
                ],
                expected: 'Performance Optimization'
            },
            {
                name: "General Question",
                messages: [
                    { role: 'user', content: 'How do I create a new customer record?' },
                    { role: 'assistant', content: 'To create a new customer record, you can use an INSERT statement or the application interface.' }
                ],
                expected: 'Create a new customer record'
            },
            {
                name: "Simple Greeting",
                messages: [
                    { role: 'user', content: 'Hello' },
                    { role: 'assistant', content: 'Hi! How can I help you today?' }
                ],
                expected: 'Chat Discussion'
            },
            {
                name: "Data Analysis Request",
                messages: [
                    { role: 'user', content: 'I need to analyze sales trends for the last quarter' },
                    { role: 'assistant', content: 'I can help you analyze sales trends. Let\'s look at the quarterly data and identify patterns.' }
                ],
                expected: 'Data Analysis'
            }
        ];

        function runAllTests() {
            let html = '<h2>Test Results</h2>';
            
            testConversations.forEach((test, index) => {
                const generatedTitle = generateTitleFromConversation(test.messages);
                const isCorrect = generatedTitle === test.expected;
                
                html += `
                    <div class="test-case">
                        <h3>Test ${index + 1}: ${test.name}</h3>
                        <div class="conversation">
                            <div class="user-msg">User: "${test.messages[0].content}"</div>
                            <div class="assistant-msg">Assistant: "${test.messages[1].content}"</div>
                        </div>
                        <div class="title-result">
                            Generated Title: "${generatedTitle}"
                        </div>
                        <div style="color: ${isCorrect ? 'green' : 'orange'};">
                            Expected: "${test.expected}" ${isCorrect ? '✅' : '⚠️'}
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('testResults').innerHTML = html;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        // Auto-run tests on page load
        window.onload = function() {
            runAllTests();
        };
    </script>
</body>
</html>
