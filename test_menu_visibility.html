<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu Visibility Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f9f9f9;
        }
        .chat-item-demo {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 8px;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            background: transparent;
            border: 1px solid transparent;
            position: relative;
            overflow: visible;
            margin-bottom: 1px;
            background-color: white;
        }
        .chat-item-demo:hover {
            background-color: #f5f5f5;
        }
        .chat-actions-demo {
            position: relative;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s;
            z-index: 10;
        }
        .chat-item-demo:hover .chat-actions-demo {
            opacity: 1;
        }
        .menu-btn-demo {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            color: #666;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
        }
        .menu-btn-demo:hover {
            background: rgba(168, 85, 247, 0.15);
            color: #a855f7;
        }
        .chat-menu-demo {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            min-width: 140px;
            padding: 4px;
            margin-top: 4px;
            animation: menuSlideIn 0.15s ease-out;
        }
        @keyframes menuSlideIn {
            from {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        .chat-menu-demo button {
            width: 100%;
            background: none;
            border: none;
            color: #333;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 4px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            text-align: left;
            margin-bottom: 1px;
        }
        .chat-menu-demo button:hover {
            background: #f5f5f5;
        }
        .chat-menu-demo button.delete-option {
            color: #dc3545;
        }
        .chat-menu-demo button.delete-option:hover {
            background: #ffeaea;
            color: #dc3545;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
    </style>
</head>
<body>
    <h1>Menu Visibility Test</h1>
    
    <div class="status info">
        <strong>🧪 Testing the 3-dot menu visibility issue</strong><br>
        This page demonstrates how the menu should work and helps identify the problem.
    </div>
    
    <div class="test-container">
        <h2>✅ Working Demo (Expected Behavior)</h2>
        <p>Hover over the chat item below and click the 3-dot menu:</p>
        
        <div class="chat-item-demo" id="demo-chat">
            <span>Sample Chat Title</span>
            <div class="chat-actions-demo">
                <button class="menu-btn-demo" onclick="toggleDemoMenu()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="1"/>
                        <circle cx="12" cy="5" r="1"/>
                        <circle cx="12" cy="19" r="1"/>
                    </svg>
                </button>
                
                <div class="chat-menu-demo" id="demo-menu" style="display: none;">
                    <button onclick="alert('Rename clicked')">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                            <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                        </svg>
                        Rename
                    </button>
                    <button onclick="alert('Share clicked')">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                            <polyline points="16,6 12,2 8,6"/>
                            <line x1="12" y1="2" x2="12" y2="15"/>
                        </svg>
                        Share
                    </button>
                    <button onclick="alert('Archive clicked')">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <path d="M9 9h6v6H9z"/>
                        </svg>
                        Archive
                    </button>
                    <button onclick="alert('Delete clicked')" class="delete-option">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z"/>
                            <path d="M10 11v6M14 11v6"/>
                        </svg>
                        Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🔍 Diagnostic Information</h2>
        
        <h3>Fixed Issues:</h3>
        <div class="status success">
            <strong>✅ Sidebar Width Reduced:</strong> From 320px to 260px (more space for main content)
        </div>
        <div class="status success">
            <strong>✅ Overflow Fixed:</strong> Changed chat-item from overflow:hidden to overflow:visible
        </div>
        <div class="status success">
            <strong>✅ Z-index Improved:</strong> Menu has z-index: 9999 to appear above other elements
        </div>
        <div class="status success">
            <strong>✅ CSS Simplified:</strong> Used absolute values instead of CSS variables for debugging
        </div>
        
        <h3>Key Changes Made:</h3>
        <ul>
            <li><strong>Sidebar width:</strong> 320px → 260px</li>
            <li><strong>Sidebar padding:</strong> var(--space-8) → var(--space-6)</li>
            <li><strong>Chat item overflow:</strong> hidden → visible</li>
            <li><strong>Menu z-index:</strong> 1000 → 9999</li>
            <li><strong>Menu background:</strong> CSS variables → white</li>
            <li><strong>Menu border:</strong> CSS variables → #ddd</li>
        </ul>
    </div>
    
    <div class="test-container">
        <h2>🎯 Testing Instructions</h2>
        <ol>
            <li><strong>Test the demo above</strong> - It should work perfectly</li>
            <li><strong>Open the main app:</strong> <a href="http://localhost:5174" target="_blank">http://localhost:5174</a></li>
            <li><strong>Hover over any chat item</strong> in the sidebar</li>
            <li><strong>Look for the 3-dot button</strong> (⋮) on the right side</li>
            <li><strong>Click the 3-dot button</strong></li>
            <li><strong>Verify the menu appears</strong> with options: Rename, Share, Archive, Delete</li>
            <li><strong>Test each menu option</strong></li>
            <li><strong>Click outside</strong> to close the menu</li>
        </ol>
        
        <div class="status info">
            <strong>💡 If the menu still doesn't appear:</strong><br>
            1. Check browser console for errors<br>
            2. Inspect element to see if menu HTML is being generated<br>
            3. Check if CSS is being applied correctly<br>
            4. Verify JavaScript state management is working
        </div>
    </div>

    <script>
        let menuOpen = false;
        
        function toggleDemoMenu() {
            const menu = document.getElementById('demo-menu');
            menuOpen = !menuOpen;
            menu.style.display = menuOpen ? 'block' : 'none';
        }
        
        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('demo-menu');
            const chatItem = document.getElementById('demo-chat');
            
            if (!chatItem.contains(event.target)) {
                menu.style.display = 'none';
                menuOpen = false;
            }
        });
    </script>
</body>
</html>
