<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Test - Re-rendering Detection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            margin: 10px 5px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .metric {
            padding: 10px;
            background-color: #e3f2fd;
            border-radius: 3px;
            text-align: center;
        }
        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #1976d2;
        }
        .metric-label {
            font-size: 0.9em;
            color: #666;
        }
        #console-output {
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Performance Test - Re-rendering Detection</h1>
    
    <div class="test-section">
        <h2>Frontend Application Performance</h2>
        <p>This test monitors the frontend application for excessive re-rendering and performance issues.</p>
        
        <div class="metrics">
            <div class="metric">
                <div class="metric-value" id="render-count">0</div>
                <div class="metric-label">Render Count</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="api-calls">0</div>
                <div class="metric-label">API Calls</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="memory-usage">0 MB</div>
                <div class="metric-label">Memory Usage</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="performance-score">Good</div>
                <div class="metric-label">Performance</div>
            </div>
        </div>
        
        <div>
            <button onclick="startMonitoring()">Start Monitoring</button>
            <button onclick="stopMonitoring()">Stop Monitoring</button>
            <button onclick="testChatSelection()">Test Chat Selection</button>
            <button onclick="testNewChat()">Test New Chat</button>
            <button onclick="clearConsole()">Clear Console</button>
        </div>
        
        <div id="status" class="status success">Ready to start monitoring</div>
    </div>
    
    <div class="test-section">
        <h3>Console Output</h3>
        <div id="console-output"></div>
    </div>

    <script>
        let monitoring = false;
        let renderCount = 0;
        let apiCallCount = 0;
        let startTime = Date.now();
        let originalConsoleLog = console.log;
        
        // Override console.log to capture render information
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            
            if (monitoring) {
                const message = args.join(' ');
                
                // Count renders
                if (message.includes('ChatSidebar render:') || message.includes('App component rendering')) {
                    renderCount++;
                    updateMetrics();
                    logToConsole(`[RENDER] ${message}`);
                }
                
                // Count API calls
                if (message.includes('API call:') || message.includes('fetch') || message.includes('request')) {
                    apiCallCount++;
                    updateMetrics();
                    logToConsole(`[API] ${message}`);
                }
                
                // Log other important messages
                if (message.includes('Error') || message.includes('Warning') || message.includes('Creating') || message.includes('Updating')) {
                    logToConsole(`[INFO] ${message}`);
                }
            }
        };
        
        function startMonitoring() {
            monitoring = true;
            renderCount = 0;
            apiCallCount = 0;
            startTime = Date.now();
            
            updateStatus('Monitoring started - watching for re-renders and performance issues', 'success');
            logToConsole('=== MONITORING STARTED ===');
            updateMetrics();
            
            // Monitor memory usage
            setInterval(() => {
                if (monitoring && performance.memory) {
                    updateMemoryUsage();
                }
            }, 1000);
        }
        
        function stopMonitoring() {
            monitoring = false;
            const duration = (Date.now() - startTime) / 1000;
            
            updateStatus(`Monitoring stopped. Duration: ${duration}s, Renders: ${renderCount}, API Calls: ${apiCallCount}`, 'warning');
            logToConsole('=== MONITORING STOPPED ===');
            
            // Analyze performance
            analyzePerformance(duration);
        }
        
        function testChatSelection() {
            if (!monitoring) {
                updateStatus('Start monitoring first!', 'error');
                return;
            }
            
            logToConsole('=== TESTING CHAT SELECTION ===');
            updateStatus('Testing chat selection - check for excessive re-renders', 'warning');
            
            // Simulate multiple chat selections
            setTimeout(() => {
                logToConsole('Simulating chat selection clicks...');
                // This would trigger actual chat selections in the real app
                // For now, we'll just log the test
                logToConsole('Chat selection test completed');
            }, 1000);
        }
        
        function testNewChat() {
            if (!monitoring) {
                updateStatus('Start monitoring first!', 'error');
                return;
            }
            
            logToConsole('=== TESTING NEW CHAT CREATION ===');
            updateStatus('Testing new chat creation - monitoring performance', 'warning');
            
            setTimeout(() => {
                logToConsole('Simulating new chat creation...');
                logToConsole('New chat test completed');
            }, 1000);
        }
        
        function updateMetrics() {
            document.getElementById('render-count').textContent = renderCount;
            document.getElementById('api-calls').textContent = apiCallCount;
        }
        
        function updateMemoryUsage() {
            if (performance.memory) {
                const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                document.getElementById('memory-usage').textContent = memoryMB + ' MB';
            }
        }
        
        function analyzePerformance(duration) {
            const rendersPerSecond = renderCount / duration;
            const apiCallsPerSecond = apiCallCount / duration;
            
            let score = 'Good';
            let scoreClass = 'success';
            
            if (rendersPerSecond > 5) {
                score = 'Poor - Too many renders';
                scoreClass = 'error';
            } else if (rendersPerSecond > 2) {
                score = 'Fair - Some excess renders';
                scoreClass = 'warning';
            }
            
            if (apiCallsPerSecond > 3) {
                score = 'Poor - Too many API calls';
                scoreClass = 'error';
            }
            
            document.getElementById('performance-score').textContent = score;
            document.getElementById('performance-score').parentElement.className = `metric ${scoreClass}`;
            
            logToConsole(`PERFORMANCE ANALYSIS:`);
            logToConsole(`- Renders per second: ${rendersPerSecond.toFixed(2)}`);
            logToConsole(`- API calls per second: ${apiCallsPerSecond.toFixed(2)}`);
            logToConsole(`- Overall score: ${score}`);
        }
        
        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function logToConsole(message) {
            const consoleEl = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            consoleEl.textContent += `[${timestamp}] ${message}\n`;
            consoleEl.scrollTop = consoleEl.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console-output').textContent = '';
        }
        
        // Auto-start monitoring when page loads
        window.onload = function() {
            updateStatus('Performance monitor ready. Open the main app in another tab and start monitoring.', 'success');
            logToConsole('Performance monitor initialized');
            logToConsole('Instructions:');
            logToConsole('1. Open http://localhost:5174 in another tab');
            logToConsole('2. Click "Start Monitoring" here');
            logToConsole('3. Interact with the chat app');
            logToConsole('4. Watch for excessive renders or API calls');
        };
    </script>
</body>
</html>
