<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chat Title Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .input {
            font-weight: bold;
            color: #333;
        }
        .output {
            color: #007bff;
            margin-top: 5px;
        }
        .generic {
            color: #dc3545;
        }
        .good {
            color: #28a745;
        }
        button {
            margin: 10px 5px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Chat Title Generation Test</h1>
    
    <div>
        <h2>Test Custom Message</h2>
        <input type="text" id="customMessage" placeholder="Enter a message to test title generation..." />
        <button onclick="testCustomMessage()">Generate Title</button>
        <div id="customResult"></div>
    </div>

    <div>
        <h2>Predefined Test Cases</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <div id="testResults"></div>
    </div>

    <script>
        // Copy the title generation functions from the actual code
        const GENERIC_TITLES = [
            'New Chat',
            'hello',
            'hi',
            'hey',
            'test',
            'testing',
            'chat',
            'conversation'
        ];

        const isGenericTitle = (title) => {
            if (!title || typeof title !== 'string') return true;
            
            const normalizedTitle = title.toLowerCase().trim();
            
            if (GENERIC_TITLES.includes(normalizedTitle)) return true;
            
            const greetingPattern = /^(hi|hello|hey|hiya|howdy)[\s!.]*$/i;
            if (greetingPattern.test(normalizedTitle)) return true;
            
            if (normalizedTitle.length <= 2) return true;
            
            return false;
        };

        const generateTitleFromMessage = (message) => {
            if (!message || typeof message !== 'string') {
                return 'New Chat';
            }

            let cleanMessage = message.trim();
            
            const prefixesToRemove = [
                /^(can you|could you|please|would you|will you)\s+/i,
                /^(i want to|i need to|i would like to|i'd like to)\s+/i,
                /^(help me|show me|tell me|explain)\s+/i,
                /^(what is|what are|how do|how can|where is)\s+/i
            ];
            
            prefixesToRemove.forEach(pattern => {
                cleanMessage = cleanMessage.replace(pattern, '');
            });
            
            cleanMessage = cleanMessage.replace(/[?!]+$/, '');
            cleanMessage = cleanMessage.charAt(0).toUpperCase() + cleanMessage.slice(1);
            
            if (cleanMessage.length > 50) {
                const truncated = cleanMessage.substring(0, 47);
                const lastSpace = truncated.lastIndexOf(' ');
                if (lastSpace > 20) {
                    cleanMessage = truncated.substring(0, lastSpace) + '...';
                } else {
                    cleanMessage = truncated + '...';
                }
            }
            
            if (cleanMessage.length < 3 || isGenericTitle(cleanMessage)) {
                return generateFallbackTitle(message);
            }
            
            return cleanMessage;
        };

        const generateFallbackTitle = (message) => {
            const lowerMessage = message.toLowerCase();
            
            if (lowerMessage.includes('select') || lowerMessage.includes('query') || lowerMessage.includes('database')) {
                return 'Database Query';
            }
            
            if (lowerMessage.includes('table') || lowerMessage.includes('column')) {
                return 'Table Analysis';
            }
            
            if (lowerMessage.includes('data') || lowerMessage.includes('record')) {
                return 'Data Exploration';
            }
            
            if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
                return 'Help Request';
            }
            
            if (lowerMessage.includes('explain') || lowerMessage.includes('what')) {
                return 'Question';
            }
            
            const now = new Date();
            const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            return `Chat ${timeString}`;
        };

        const testCases = [
            { input: "hello", expected: "should be generic" },
            { input: "New Chat", expected: "should be generic" },
            { input: "Can you help me find all users in the database?", expected: "Find all users in the database" },
            { input: "What is the total revenue for last month?", expected: "The total revenue for last month" },
            { input: "SELECT * FROM customers WHERE age > 25", expected: "Database Query" },
            { input: "Show me the table structure", expected: "Table Analysis" },
            { input: "I need to analyze customer data trends over the past year", expected: "Analyze customer data trends over the past year" },
            { input: "How do I create a new user account?", expected: "Create a new user account" },
            { input: "This is a very long message that should be truncated because it exceeds the maximum length limit for chat titles", expected: "should be truncated" },
            { input: "hi there!", expected: "should be generic" },
            { input: "test", expected: "should be generic" }
        ];

        function testCustomMessage() {
            const message = document.getElementById('customMessage').value;
            if (!message.trim()) {
                alert('Please enter a message to test');
                return;
            }

            const title = generateTitleFromMessage(message);
            const isGeneric = isGenericTitle(title);
            
            document.getElementById('customResult').innerHTML = `
                <div class="test-case">
                    <div class="input">Input: "${message}"</div>
                    <div class="output">Generated Title: "${title}"</div>
                    <div class="${isGeneric ? 'generic' : 'good'}">
                        ${isGeneric ? '❌ Generic title' : '✅ Good title'}
                    </div>
                </div>
            `;
        }

        function runAllTests() {
            let html = '';
            
            testCases.forEach((testCase, index) => {
                const title = generateTitleFromMessage(testCase.input);
                const isGeneric = isGenericTitle(title);
                
                let status = '✅';
                let statusClass = 'good';
                
                if (testCase.expected === "should be generic" && !isGeneric) {
                    status = '❌ Expected generic';
                    statusClass = 'generic';
                } else if (testCase.expected === "should be truncated" && title.length <= 50) {
                    status = '❌ Expected truncation';
                    statusClass = 'generic';
                } else if (testCase.expected !== "should be generic" && testCase.expected !== "should be truncated" && title !== testCase.expected) {
                    status = `⚠️ Expected: "${testCase.expected}"`;
                    statusClass = 'generic';
                }
                
                html += `
                    <div class="test-case">
                        <div class="input">Test ${index + 1}: "${testCase.input}"</div>
                        <div class="output">Generated: "${title}"</div>
                        <div class="${statusClass}">${status}</div>
                    </div>
                `;
            });
            
            document.getElementById('testResults').innerHTML = html;
        }

        // Auto-run tests on page load
        window.onload = function() {
            runAllTests();
        };
    </script>
</body>
</html>
