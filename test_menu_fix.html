<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f9f9f9;
        }
        
        /* Replicate the exact chat item styling */
        .demo-chat-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 4px 8px;
            border-radius: 12px;
            transition: all 0.15s;
            cursor: pointer;
            margin-bottom: 1px;
            position: relative;
            overflow: visible;
            background: white;
            width: 280px; /* New reduced width */
        }
        
        .demo-chat-item:hover {
            background: rgba(168, 85, 247, 0.08);
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(168, 85, 247, 0.15);
        }
        
        .demo-chat-actions {
            position: relative;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s;
            z-index: 10;
            flex-shrink: 0;
        }
        
        .demo-chat-item:hover .demo-chat-actions,
        .demo-chat-actions:hover {
            opacity: 1;
        }
        
        .demo-menu-btn {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.15s;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
        }
        
        .demo-menu-btn:hover {
            background: rgba(168, 85, 247, 0.15);
            color: #a855f7;
            transform: scale(1.1);
            box-shadow: 0 2px 4px rgba(168, 85, 247, 0.2);
        }
        
        .demo-chat-menu {
            position: absolute;
            top: 100%;
            right: -10px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            min-width: 140px;
            padding: 4px;
            margin-top: 4px;
            animation: menuSlideIn 0.15s ease-out;
            pointer-events: auto;
        }
        
        @keyframes menuSlideIn {
            from {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .demo-chat-menu button {
            width: 100%;
            background: none;
            border: none;
            color: #333;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 4px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            text-align: left;
            margin-bottom: 1px;
            white-space: nowrap;
            pointer-events: auto;
        }
        
        .demo-chat-menu button:hover {
            background: #f5f5f5;
        }
        
        .demo-chat-menu button.delete-option {
            color: #dc3545;
        }
        
        .demo-chat-menu button.delete-option:hover {
            background: #ffeaea;
            color: #dc3545;
        }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>🔧 Menu Fix Test - Reduced Size + Working Menu</h1>
    
    <div class="status success">
        <strong>✅ Issues Fixed:</strong><br>
        ✅ Left panel reduced from 320px to 280px<br>
        ✅ Menu positioning simplified (absolute instead of fixed)<br>
        ✅ Menu positioned to the right side (-10px offset)<br>
        ✅ Pointer events enabled for reliable clicking
    </div>
    
    <div class="test-container">
        <h2>🧪 Test the Menu</h2>
        <p>Hover over the chat item below and click the 3-dot menu:</p>
        
        <div class="demo-chat-item" id="test-chat">
            <span>SQL Query Analysis</span>
            <div class="demo-chat-actions">
                <button class="demo-menu-btn" onclick="toggleTestMenu()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="1"/>
                        <circle cx="12" cy="5" r="1"/>
                        <circle cx="12" cy="19" r="1"/>
                    </svg>
                </button>
                
                <div class="demo-chat-menu" id="test-menu" style="display: none;">
                    <button onclick="testAction('Rename')">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                            <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                        </svg>
                        Rename
                    </button>
                    <button onclick="testAction('Share')">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                            <polyline points="16,6 12,2 8,6"/>
                            <line x1="12" y1="2" x2="12" y2="15"/>
                        </svg>
                        Share
                    </button>
                    <button onclick="testAction('Archive')">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <path d="M9 9h6v6H9z"/>
                        </svg>
                        Archive
                    </button>
                    <button onclick="testAction('Delete')" class="delete-option">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z"/>
                            <path d="M10 11v6M14 11v6"/>
                        </svg>
                        Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="status info">
        <h3>🔧 Technical Changes Made:</h3>
        <ul>
            <li><strong>Sidebar Width:</strong> 320px → 280px (40px smaller)</li>
            <li><strong>Sidebar Padding:</strong> var(--space-8) → var(--space-6)</li>
            <li><strong>Menu Position:</strong> Fixed → Absolute (right: -10px)</li>
            <li><strong>Menu Z-index:</strong> Increased to 10000</li>
            <li><strong>Pointer Events:</strong> Explicitly enabled</li>
            <li><strong>JavaScript:</strong> Simplified toggle function</li>
        </ul>
    </div>
    
    <div class="status warning">
        <h3>🧪 Testing Steps:</h3>
        <ol>
            <li><strong>Test above demo:</strong> Should work perfectly</li>
            <li><strong>Open main app:</strong> <a href="http://localhost:5174" target="_blank">http://localhost:5174</a></li>
            <li><strong>Check sidebar size:</strong> Should be noticeably smaller</li>
            <li><strong>Hover over chat items:</strong> 3-dot button should appear</li>
            <li><strong>Click 3-dot menu:</strong> Menu should appear to the right</li>
            <li><strong>Click menu options:</strong> Should work without disappearing</li>
        </ol>
    </div>
    
    <div id="test-result" class="status info" style="display: none;">
        <strong>Test Result:</strong> <span id="result-text"></span>
    </div>

    <script>
        let menuOpen = false;
        
        function toggleTestMenu() {
            const menu = document.getElementById('test-menu');
            menuOpen = !menuOpen;
            menu.style.display = menuOpen ? 'block' : 'none';
        }
        
        function testAction(action) {
            const resultDiv = document.getElementById('test-result');
            const resultText = document.getElementById('result-text');
            
            resultText.textContent = `${action} action clicked successfully! ✅`;
            resultDiv.style.display = 'block';
            resultDiv.className = 'status success';
            
            // Hide menu
            document.getElementById('test-menu').style.display = 'none';
            menuOpen = false;
            
            // Hide result after 3 seconds
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 3000);
        }
        
        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const chatItem = document.getElementById('test-chat');
            if (!chatItem.contains(event.target)) {
                document.getElementById('test-menu').style.display = 'none';
                menuOpen = false;
            }
        });
    </script>
</body>
</html>
