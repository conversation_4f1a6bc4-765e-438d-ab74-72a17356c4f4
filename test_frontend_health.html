<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Health Check</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            margin: 10px 5px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 200px;
        }
    </style>
</head>
<body>
    <h1>Frontend Health Check</h1>
    
    <div>
        <button onclick="testFrontendAccess()">Test Frontend Access</button>
        <button onclick="testBackendAPI()">Test Backend API</button>
        <button onclick="testFullFlow()">Test Complete Flow</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        let results = [];

        function addResult(type, message, details = null) {
            const timestamp = new Date().toLocaleTimeString();
            results.push({
                type,
                message,
                details,
                timestamp
            });
            updateDisplay();
        }

        function updateDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = results.map(result => `
                <div class="test-result ${result.type}">
                    <strong>[${result.timestamp}]</strong> ${result.message}
                    ${result.details ? `<pre>${result.details}</pre>` : ''}
                </div>
            `).join('');
        }

        function clearResults() {
            results = [];
            updateDisplay();
        }

        async function testFrontendAccess() {
            addResult('info', 'Testing frontend access...');
            
            try {
                const response = await fetch('http://localhost:5174/');
                if (response.ok) {
                    addResult('success', 'Frontend is accessible on port 5174');
                } else {
                    addResult('error', `Frontend returned status: ${response.status}`);
                }
            } catch (error) {
                addResult('error', 'Frontend is not accessible', error.message);
                
                // Try alternative port
                try {
                    const altResponse = await fetch('http://localhost:5173/');
                    if (altResponse.ok) {
                        addResult('warning', 'Frontend is accessible on port 5173 instead');
                    }
                } catch (altError) {
                    addResult('error', 'Frontend not accessible on port 5173 either');
                }
            }
        }

        async function testBackendAPI() {
            addResult('info', 'Testing backend API endpoints...');
            
            const endpoints = [
                { url: 'http://localhost:8000/health', name: 'Health Check' },
                { url: 'http://localhost:8000/chats', name: 'Chats List' },
                { url: 'http://localhost:8000/database/tables', name: 'Database Tables' }
            ];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url);
                    if (response.ok) {
                        const data = await response.json();
                        addResult('success', `${endpoint.name}: OK`);
                        
                        if (endpoint.name === 'Chats List') {
                            addResult('info', `Found ${data.length} chats`);
                        }
                    } else {
                        addResult('error', `${endpoint.name}: HTTP ${response.status}`);
                    }
                } catch (error) {
                    addResult('error', `${endpoint.name}: Connection failed`, error.message);
                }
            }
        }

        async function testFullFlow() {
            addResult('info', 'Testing complete application flow...');
            
            try {
                // Test 1: Load chats
                const chatsResponse = await fetch('http://localhost:8000/chats');
                if (!chatsResponse.ok) {
                    throw new Error(`Chats API failed: ${chatsResponse.status}`);
                }
                const chats = await chatsResponse.json();
                addResult('success', `Step 1: Loaded ${chats.length} chats`);

                if (chats.length > 0) {
                    // Test 2: Load messages for first chat
                    const firstChat = chats[0];
                    const messagesResponse = await fetch(`http://localhost:8000/chats/${firstChat.id}/messages`);
                    if (!messagesResponse.ok) {
                        throw new Error(`Messages API failed: ${messagesResponse.status}`);
                    }
                    const messages = await messagesResponse.json();
                    addResult('success', `Step 2: Loaded ${messages.length} messages for chat "${firstChat.title}"`);

                    // Test 3: Test localStorage simulation
                    try {
                        localStorage.setItem('test_chat_data', JSON.stringify({
                            chats: chats,
                            currentChat: firstChat,
                            messages: { [firstChat.id]: messages }
                        }));
                        
                        const stored = JSON.parse(localStorage.getItem('test_chat_data'));
                        if (stored && stored.chats && stored.currentChat && stored.messages) {
                            addResult('success', 'Step 3: localStorage persistence working');
                            localStorage.removeItem('test_chat_data'); // cleanup
                        } else {
                            throw new Error('Data not properly stored');
                        }
                    } catch (storageError) {
                        addResult('error', 'Step 3: localStorage failed', storageError.message);
                    }

                    // Test 4: Database tables
                    const tablesResponse = await fetch('http://localhost:8000/database/tables');
                    if (tablesResponse.ok) {
                        const tables = await tablesResponse.json();
                        addResult('success', `Step 4: Loaded ${tables.length} database tables`);
                    } else {
                        addResult('warning', 'Step 4: Database tables endpoint failed');
                    }

                    addResult('success', '🎉 Complete flow test passed!');
                } else {
                    addResult('warning', 'No chats available for full flow test');
                }

            } catch (error) {
                addResult('error', 'Complete flow test failed', error.message);
            }
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            addResult('info', 'Starting automatic health checks...');
            setTimeout(() => {
                testFrontendAccess();
                setTimeout(() => {
                    testBackendAPI();
                }, 1000);
            }, 500);
        };
    </script>
</body>
</html>
