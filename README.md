# My SaaS Frontend

React-based frontend application built with <PERSON>ite and TanStack Query.

## Setup for New Developers

### Prerequisites
1. **Install IDE** - We recommend VS Code
2. **Install Docker Desktop** - Required for containerized development

### Project Setup
1. **Clone the repository**
```bash
git clone <your-repo-url>
cd My_SaaS_Frontend
```

2. **Create Docker volume**
```bash
docker volume create saas-frontend-node-modules
```

3. **Configure environment variables**
```bash
cp .env.example .env.docker
```
Edit `.env.docker`:
```env
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_ENV=development
```

4. **Start the application**
```bash
docker compose up --build -d
```

5. **Verify setup by running tests**
```bash
docker exec -it my_saas_frontend-frontend-1 /bin/bash
npm test
```

## Development Commands

### Daily Development
- **Start development** - `docker compose up --build -d`
- **Stop containers** - `docker compose down`
- **View container logs** - `docker compose logs -f`
- **Access container** - `docker exec -it my_saas_frontend-frontend-1 /bin/bash`

### Testing & Building
- **Run tests** - `npm test`
- **Run specific test** - `npm test -- --run src/components/ComponentName.test.jsx`
- **Build for production** - `npm run build`
- **Preview production build** - `npm run preview`

### Docker Management
- **View all containers** - `docker ps -a`
- **Stop all containers** - `docker stop $(docker ps -aq)`
- **Clean up containers** - `docker system prune -a`

## Environment Variables
- All variables must be prefixed with `VITE_`
- Never commit sensitive data to environment files

## Access
- **Application**: http://localhost:5173
- **Development Tools**: Available in browser dev tools
