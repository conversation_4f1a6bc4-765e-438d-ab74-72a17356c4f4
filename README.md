# My SaaS Backend API

FastAPI-based backend with MySQL database and OpenAI integration.

## Setup for New Developers

### Prerequisites
1. **Install IDE** - We recommend PyCharm or VS Code
2. **Install Docker Desktop** - Required for containerized development

### Project Setup
1. **Clone the repository**
```bash
git clone <your-repository-url>
cd My_SaaS_Backend
```

2. **Configure environment variables**
```bash
cp .env.production .env
# Edit .env with your configuration (never commit this file)
```

3. **Start the application**
```bash
# Development
docker-compose -f deployment/docker/docker-compose.dev.yml up --build -d

# Production
docker-compose -f deployment/docker/docker-compose.prod.yml up --build -d
```

4. **Setup and verify with testing**
```bash
docker exec -it my_saas_backend-app-1 /bin/bash
python scripts/setup_mysql_all_envs.py --env test
python scripts/run_tests.py --type all
```

## Development Commands

### Daily Development
- **Start development** - `docker-compose -f deployment/docker/docker-compose.dev.yml up --build -d`
- **Stop containers** - `docker-compose down`
- **View container logs** - `docker-compose logs -f`
- **Access API container** - `docker exec -it my_saas_backend-app-1 /bin/bash`

### Database Operations
- **Run migrations** - `alembic upgrade head`
- **Generate migration** - `alembic revision --autogenerate -m "description"`
- **Access MySQL (dev)** - `docker exec -it my_saas_backend-mysql_dev-1 mysql -u dev_user -p`

### Testing
- **Run all tests** - `python scripts/run_tests.py --type all`
- **Run unit tests** - `python scripts/run_tests.py --type unit --verbose`

### Docker Management
- **View all containers** - `docker ps -a`
- **Stop all containers** - `docker stop $(docker ps -aq)`
- **Clean up containers** - `docker system prune -a`

## Environment Variables
- Never commit `.env` files with real secrets
- Use `.env.production` template for production setup
- Generate secure JWT secrets: `python -c "import secrets; print(secrets.token_urlsafe(64))"`

## Access
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Database Health**: http://localhost:8000/database/mysql/health

## Documentation
- [Architecture](docs/ARCHITECTURE.md) - Repository structure and design
- [Production Deployment](docs/PRODUCTION_DEPLOYMENT_GUIDE.md) - Complete production setup
- [Security Guide](docs/SECURITY_GUIDE.md) - Security implementation
- [Testing Guide](docs/TESTING_GUIDE.md) - Testing framework