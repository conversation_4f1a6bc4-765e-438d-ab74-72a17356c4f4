"""
Enhanced AI methods for the AIMessageProcessor class.
These methods provide advanced context-aware processing capabilities.
"""

import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


def classify_message_intent_enhanced(self, message: str, enhanced_context: Dict[str, Any]) -> Dict[str, Any]:
    """Enhanced intent classification using database context."""
    try:
        # Build enhanced prompt with database context
        schema_info = enhanced_context.get("schema_context", {})
        business_info = enhanced_context.get("business_context", {})
        
        classification_prompt = f"""
        Analyze this user message and classify its intent with enhanced database context.
        
        User Message: "{message}"
        
        Database Context:
        - Business Domain: {business_info.get('business_domain', 'general')}
        - Available Tables: {[t['name'] for t in schema_info.get('relevant_tables', [])]}
        - Table Descriptions: {[f"{t['name']}: {t['description']}" for t in schema_info.get('relevant_tables', [])]}
        - User Preferences: {enhanced_context.get('user_context', {})}
        
        Classify the intent into one of these categories:
        1. DATABASE_QUERY - Requests for data, SQL queries, data analysis
        2. SCHEMA_INQUIRY - Questions about database structure, tables, columns
        3. BUSINESS_QUESTION - Questions about business logic or data meaning
        4. HELP_REQUEST - Asking for help, capabilities, instructions
        5. GREETING - Greetings, hellos, how are you, etc.
        6. GENERAL_CONVERSATION - Other conversational messages
        
        Consider the available tables and business context when determining if this is a database query.
        
        Respond with a JSON object containing:
        {{
            "intent": "CATEGORY_NAME",
            "confidence": 0.95,
            "reasoning": "Brief explanation considering database context",
            "is_database_related": true/false,
            "requires_sql": true/false,
            "suggested_tables": ["table1", "table2"],
            "suggested_response_type": "sql_generation/informational/conversational"
        }}
        """
        
        response = self.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are an expert at understanding user intent with database context. Always respond with valid JSON."},
                {"role": "user", "content": classification_prompt}
            ],
            temperature=0.1,
            max_tokens=400
        )
        
        ai_response = response.choices[0].message.content.strip()
        if ai_response.startswith("```json"):
            ai_response = ai_response.replace("```json", "").replace("```", "").strip()
        
        intent_data = json.loads(ai_response)
        
        return {
            "success": True,
            "intent": intent_data.get("intent", "GENERAL_CONVERSATION"),
            "confidence": intent_data.get("confidence", 0.5),
            "reasoning": intent_data.get("reasoning", ""),
            "is_database_related": intent_data.get("is_database_related", False),
            "requires_sql": intent_data.get("requires_sql", False),
            "suggested_tables": intent_data.get("suggested_tables", []),
            "suggested_response_type": intent_data.get("suggested_response_type", "conversational"),
            "raw_ai_response": ai_response
        }
        
    except Exception as e:
        logger.error(f"Error in enhanced intent classification: {e}")
        return self._fallback_intent_classification(message)


def generate_sql_response_with_context(self, message: str, enhanced_context: Dict[str, Any]) -> Dict[str, Any]:
    """Generate SQL response using enhanced database context."""
    try:
        schema_info = enhanced_context.get("schema_context", {})
        business_info = enhanced_context.get("business_context", {})
        learning_info = enhanced_context.get("learning_context", {})
        
        # Build comprehensive prompt for SQL generation
        sql_prompt = f"""
        Generate a SQL query for this natural language request using the provided database context.
        
        User Request: "{message}"
        
        Database Schema Context:
        """
        
        # Add table information
        for table in schema_info.get("relevant_tables", []):
            sql_prompt += f"\nTable: {table['name']} ({table.get('description', 'No description')})\n"
            sql_prompt += "Columns:\n"
            for col in table.get("columns", []):
                sql_prompt += f"  - {col['name']} ({col['type']}) - {col.get('description', 'No description')}\n"
        
        # Add relationship information
        if schema_info.get("table_relationships"):
            sql_prompt += "\nTable Relationships:\n"
            for rel in schema_info.get("table_relationships", []):
                sql_prompt += f"  - {rel['source_table']} -> {rel['target_table']} ({rel['description']})\n"
        
        # Add business context
        sql_prompt += f"\nBusiness Context:\n"
        sql_prompt += f"  - Domain: {business_info.get('business_domain', 'general')}\n"
        sql_prompt += f"  - Preferred Style: {business_info.get('preferred_query_style', 'simple')}\n"
        
        # Add learning context
        if learning_info.get("successful_patterns"):
            sql_prompt += f"\nSuccessful Query Patterns (for reference):\n"
            for pattern in learning_info.get("successful_patterns", [])[:3]:
                sql_prompt += f"  - \"{pattern['nl_query']}\" -> {pattern['sql_query']}\n"
        
        sql_prompt += f"""
        
        Generate a SQL query that:
        1. Answers the user's question accurately
        2. Uses appropriate table joins based on relationships
        3. Follows the business context and user preferences
        4. Is optimized and follows best practices
        
        Respond with a JSON object:
        {{
            "sql_query": "SELECT ...",
            "explanation": "Brief explanation of what the query does",
            "tables_used": ["table1", "table2"],
            "confidence": 0.95
        }}
        """
        
        response = self.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are an expert SQL developer with deep understanding of database schemas and business context."},
                {"role": "user", "content": sql_prompt}
            ],
            temperature=0.1,
            max_tokens=800
        )
        
        ai_response = response.choices[0].message.content.strip()
        if ai_response.startswith("```json"):
            ai_response = ai_response.replace("```json", "").replace("```", "").strip()
        
        sql_data = json.loads(ai_response)
        
        return {
            "sql_query": sql_data.get("sql_query"),
            "content": sql_data.get("explanation", "SQL query generated"),
            "tables_used": sql_data.get("tables_used", []),
            "confidence": sql_data.get("confidence", 0.8)
        }
        
    except Exception as e:
        logger.error(f"Error generating SQL with context: {e}")
        return {
            "sql_query": None,
            "content": "I apologize, but I encountered an error generating the SQL query. Please try rephrasing your request.",
            "tables_used": [],
            "confidence": 0.0
        }


def generate_conversational_response_with_context(self, message: str, intent_data: Dict[str, Any], 
                                                enhanced_context: Dict[str, Any]) -> Dict[str, Any]:
    """Generate conversational response using enhanced context."""
    try:
        business_info = enhanced_context.get("business_context", {})
        schema_info = enhanced_context.get("schema_context", {})
        
        response_prompt = f"""
        Generate a helpful response for this user message using the provided context.
        
        User Message: "{message}"
        Intent: {intent_data.get('intent', 'GENERAL_CONVERSATION')}
        
        Database Context:
        - Business Domain: {business_info.get('business_domain', 'general')}
        - Available Tables: {[t['name'] for t in schema_info.get('relevant_tables', [])]}
        - Response Tone: {business_info.get('response_tone', 'professional')}
        - Explanation Level: {business_info.get('explanation_level', 'intermediate')}
        
        Generate a response that:
        1. Addresses the user's message appropriately
        2. Uses the business context and terminology
        3. Matches the preferred tone and explanation level
        4. Provides helpful information about available data if relevant
        
        Keep the response conversational and helpful.
        """
        
        response = self.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful database assistant with knowledge of the user's specific business context."},
                {"role": "user", "content": response_prompt}
            ],
            temperature=0.7,
            max_tokens=500
        )
        
        ai_response = response.choices[0].message.content.strip()
        
        return {
            "content": ai_response,
            "response": ai_response
        }
        
    except Exception as e:
        logger.error(f"Error generating conversational response with context: {e}")
        return {
            "content": "I'm here to help with your database needs. What would you like to know?",
            "response": "I'm here to help with your database needs. What would you like to know?"
        }


# Monkey patch these methods to the AIMessageProcessor class
def enhance_ai_processor():
    """Add enhanced methods to the AIMessageProcessor class."""
    from .ai_message_processor import AIMessageProcessor
    
    AIMessageProcessor.classify_message_intent_enhanced = classify_message_intent_enhanced
    AIMessageProcessor.generate_sql_response_with_context = generate_sql_response_with_context
    AIMessageProcessor.generate_conversational_response_with_context = generate_conversational_response_with_context
