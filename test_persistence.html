<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Persistence Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Chat Persistence Test</h1>
    
    <div class="test-section">
        <h2>Test localStorage Functionality</h2>
        <button onclick="testLocalStorage()">Test localStorage</button>
        <button onclick="clearLocalStorage()">Clear localStorage</button>
        <button onclick="viewLocalStorage()">View localStorage</button>
        <div id="localStorage-result"></div>
    </div>

    <div class="test-section">
        <h2>Test Chat API</h2>
        <button onclick="testChatAPI()">Test Chat API</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h2>Simulate Frontend Persistence</h2>
        <button onclick="simulateFrontendFlow()">Simulate Frontend Flow</button>
        <div id="frontend-result"></div>
    </div>

    <script>
        // Storage keys (same as in the frontend)
        const STORAGE_KEYS = {
            CURRENT_CHAT: 'chat_current_chat',
            MESSAGES_CACHE: 'chat_messages_cache',
            CHATS_CACHE: 'chat_chats_cache'
        };

        function testLocalStorage() {
            const resultDiv = document.getElementById('localStorage-result');
            try {
                // Test basic localStorage functionality
                const testData = { test: 'data', timestamp: new Date().toISOString() };
                localStorage.setItem('test_key', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('test_key'));
                
                if (retrieved.test === 'data') {
                    resultDiv.innerHTML = '<div class="test-result success">✓ localStorage is working correctly</div>';
                } else {
                    resultDiv.innerHTML = '<div class="test-result error">✗ localStorage data mismatch</div>';
                }
                
                localStorage.removeItem('test_key');
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">✗ localStorage error: ${error.message}</div>`;
            }
        }

        function clearLocalStorage() {
            Object.values(STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            document.getElementById('localStorage-result').innerHTML = '<div class="test-result success">✓ Chat localStorage cleared</div>';
        }

        function viewLocalStorage() {
            const resultDiv = document.getElementById('localStorage-result');
            const data = {};
            Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
                const value = localStorage.getItem(key);
                data[name] = value ? JSON.parse(value) : null;
            });
            
            resultDiv.innerHTML = `
                <div class="test-result">
                    <strong>Current localStorage data:</strong>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
        }

        async function testChatAPI() {
            const resultDiv = document.getElementById('api-result');
            try {
                // Test getting chats
                const response = await fetch('http://localhost:8000/chats');
                if (response.ok) {
                    const chats = await response.json();
                    resultDiv.innerHTML = `
                        <div class="test-result success">✓ Chat API is working</div>
                        <div class="test-result">
                            <strong>Available chats:</strong>
                            <pre>${JSON.stringify(chats, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="test-result error">✗ Chat API error: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">✗ Chat API error: ${error.message}</div>`;
            }
        }

        async function simulateFrontendFlow() {
            const resultDiv = document.getElementById('frontend-result');
            try {
                // Step 1: Load chats from API
                const chatsResponse = await fetch('http://localhost:8000/chats');
                const chats = await chatsResponse.json();
                
                // Step 2: Store chats in localStorage
                localStorage.setItem(STORAGE_KEYS.CHATS_CACHE, JSON.stringify(chats));
                
                // Step 3: Select first chat (if available)
                if (chats.length > 0) {
                    const firstChat = chats[0];
                    localStorage.setItem(STORAGE_KEYS.CURRENT_CHAT, JSON.stringify(firstChat));
                    
                    // Step 4: Load messages for the chat
                    const messagesResponse = await fetch(`http://localhost:8000/chats/${firstChat.id}/messages`);
                    const messages = await messagesResponse.json();
                    
                    // Step 5: Store messages in localStorage
                    const messagesCache = { [firstChat.id]: messages };
                    localStorage.setItem(STORAGE_KEYS.MESSAGES_CACHE, JSON.stringify(messagesCache));
                    
                    resultDiv.innerHTML = `
                        <div class="test-result success">✓ Frontend flow simulation completed</div>
                        <div class="test-result">
                            <strong>Selected chat:</strong> ${firstChat.title}
                            <br><strong>Messages loaded:</strong> ${messages.length}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result">No chats available. Create a chat first using the Chat API test.</div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">✗ Frontend simulation error: ${error.message}</div>`;
            }
        }

        // Auto-run tests on page load
        window.onload = function() {
            testLocalStorage();
            viewLocalStorage();
        };
    </script>
</body>
</html>
